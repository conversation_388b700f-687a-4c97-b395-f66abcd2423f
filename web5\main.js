import * as AudioProcessor from './utils/audio-processor.js';
import { getStorage, setStorage } from './utils/storage.js';
import * as request from './utils/http.js';
import { detectDominantColor, rgbToHex, hexToRgb } from './utils/graphics.js';
import { 
    pauseRecording,
    resumeRecording,
    initRecorder,
    requestMicrophoneAccess,
    closeMicrophone,
    isRecording,
    isRecordingPaused,
    startRecording,
    stopRecording
} from './utils/recorder.js';
import { 
    initDetector, 
    startDetection, 
    stopDetection,
    appendDetection,
    updateConfig
} from './utils/speaker-detector.js';
// 导入VAD相关工具
import { 
    initVAD, 
    startVAD, 
    stopVAD, 
    pauseVAD, 
    resumeVAD,
    isVADRunning
} from './utils/vad.js';
import { createAudioWaveform } from './utils/audio-waveform.js';
import { elements, domInit, updateElement, updateElements } from './js/core/dom.js'
const els = elements;
import WebSocketManager, { 
    createConnection,
    CONNECTION_STATES 
} from './utils/websocket.js';
import * as webrtc from './js/core/webrtc.js';
import * as webgl from './js/core/webgl.js';
import * as canvas2d from './js/core/canvas2d.js';
import { getHotwordsJson } from './js/core/asr.js';
import { matchWakeWordByPinyin, addWakeWord } from './js/core/wake_words.js';

import LoadingAnimation from './js/components/loading-animation-component.js';
        
// 当页面加载完成后初始化加载动画
document.addEventListener('DOMContentLoaded', () => { 
    // 创建加载动画实例
    const loadingAnimation = new LoadingAnimation({
        loadingText: '数字人系统初始化中',
        autoStart: true,
        loadDuration: 500,
        duration: 750,
        onComplete: () => {
            console.log('加载动画完成，页面内容已完全显示');
        }
    });
    
    // 将加载动画实例绑定到window对象，以便其他地方可以访问
    window.loadingAnimation = loadingAnimation;
});


// ASR模块导入
import { 
    processASRMessage, 
    handleFinalASRResult, 
    initASR, 
    ASR_EVENTS, 
    addASREventListener 
} from './js/core/asr.js';

import { 
    openCamera,
    closeCamera,
    initFaceDetection,
    startDetectionLoop, 
    stopFaceDetection,
    // registerCurrentFace,
    setDetectionInterval,
    setVerificationThreshold,
    clearAllFaceProfiles,
    faceDetectionConfig,
    getFaceVerificationStatus,
    drawFaceDetections,
    reloadFaceProfiles
} from './js/core/face_detection.js';

// 定义连接类型常量
const CONNECTION_TYPES = {
    DIGITAL_HUMAN: 'dh',
    REMOTE_CONTROL: 'oc',
    SPEECH_RECOGNITION: 'asr'
};

const STORAGE_NAMESPACE = {
    FACE_CONFIG: 'digitalHuman:face:config',
    FACE_PROFILES: 'digitalHuman:face:profiles',
    DB_CONNECTIONS: 'digitalHuman:knowledge:connections',
    KNOWLEDGE_FILES: 'digitalHuman:knowledge:files'
}

// 存储WebSocket连接实例
let ocConnection = null;
let dhConnection = null;
let asrConnection = null;

// 添加全局变量用于ASR句子超时处理
let lastASRTimestamp = null;
let currentASRText = '';
const ASR_SENTENCE_TIMEOUT = 800; // 1500毫秒，即1.5秒
let asrSentenceTimeoutId = null;

// 扬声器检测相关变量
let speakerDetectionEnabled = true;
let wasPausedBySpeaker = true; // 是否因检测到扬声器声音而处于需要暂停录音的状态
let speakerVolumeThreshold = 0.001; // 音量阈值
let speakerHoldTime = 300; // 声音停止后等待恢复的时间(毫秒)

let customWakeupWord = '小幸运';

// 定义知识库相关变量
let dbConnections = [];
let uploadedKnowledgeFiles = [];

// 添加变量来跟踪点击状态
let isASRButtonTouching = false;
let asrButtonTouchTimeout = null;

// 全局拖动状态变量   
window.isDragging = false;
window.activeDragInstanceId = null;

document.addEventListener('DOMContentLoaded', function() {

    //#region 全局初始化
    domInit();
    initASR();
    
    // 初始化VAD语音活动检测
    
    //#endregion

    //#region 全局事件监听
    window.addEventListener('resize', function() {
        console.log("已监听到窗口大小变化，重新居中数字人canvas");
        centerCanvasInContainer();
        // windowResizeHandler(els.settingsModal);
    });
    //#endregion

    //#region 系统基础板块变量定义
    let pc = null;
    let videoElement = null;
    let canvas = null;
    // let gl = null;
    let animationFrameId = null; // 新增：用于存储动画帧请求ID
    let useCanvas2D = false;
    let currentCanvasContext = null;

    // 数字人是否启动完成
    let isDigitalHumanStarted = false;

    // 性能监控相关变量
    let frameCount = 0;
    let lastFpsUpdateTime = 0;
    let currentFps = 0;
    let frameProcessingTime = 0;
    const fpsUpdateInterval = 500; // 每500毫秒更新一次FPS显示

    // 初始化完成标记
    // let dhCanvasComplete = true;

    // 视频尺寸和比例
    let dhSize = {
        width: 0,
        height: 0
    };
    let isRatioLocked = false;
    let videoRatioWidth = null;
    let videoRatioHeight = null;
        
    // 新增：最大画布渲染分辨率限制
    let maxCanvasResolution = 16384; // 限制最大边长为16k大屏的像素

    // 视频处理参数
    let dhOffset = {
        x: 0,
        y: 0
    };
    let isFirstFrame = true;
    let bgColorRGB = null;
    let tolerance = 0; // 容差，建议 0-50 之间，最佳效果在 40~120 之间
    let antiAliasingStrength = 0; // 抗锯齿强度，建议 0.1-1.0 之间，推荐关闭
    let spill = 0; // 溢出，建议 0-1.0 之间，推荐关闭
    let useGreenScreen = false;
    let inputBgColorHex = false;
    let dhPreviousSizeAndPosition = {
        previousWidth: 0,
        previousHeight: 0,
        previousLeft: 0,
        previousTop: 0
    };
    // 添加一个变量来跟踪上一次的尺寸
    let lastDisplayWidth = 0;
    let lastDisplayHeight = 0;

    // let audioDelay = 0; // 音频延迟，不成熟，取消使用

    let initialWidth = 0;
    let initialHeight = 0;

    // 视频原始比例
    let aspectRatio = null;
    let customRatioLocked = false;
    // 数字人画布比例
    let customAspectRatio = null;
    let currentAspectRatio = null;
    
    // 数字人对话发送逻辑
    let messageText = null;
    let messageInterrupt = null;
    let messageType = null;

    // 数字人人设文本
    let personaText;

    // 新增：centerCanvas函数需要的DOM元素引用
    // let customWidthSlider = null;
    // let customWidthValue = null;
    // let customHeightSlider = null;
    // let customHeightValue = null;
    // let xOffsetSlider = null;
    // let xOffsetValue = null;
    // let yOffsetSlider = null;
    // let YOffsetValue = null;

    // 新增：计时器变量
    let sizeCheckTimeout = null;

    // 控制 ui 的变量
    let settingsModalOffset = {
        x: 0,
        y: 0
    }; // 模态框的偏移坐标
    let settingsModalSize = {
        width: 0,
        height: 0
    };
    let requestId; // ui 控制响应id

    // 数字人拖动
    let isSpacePressed = false;
    let isDragging = false;
    let isAltPressed = false;
    let isDraggingBg = false;
    let offsetX, offsetY;

    // 背景缩放相关变量
    let originalWidth = 0;
    let originalHeight = 0;
    let currentScale = 1;
    let scaleFactor = 0.1;
    let bgScale = null;
    let bgOffset = {
        x: null,
        y: null
    };
    let bgWidth = null;
    let bgHeight = null;
    let bgFit = "cover";

    // 系统 ui 元素
    let useBehindUi = false;
    let useBehindUiTime = 0;
    let uiHideTimer = null;

    // 无人直播相关变量
    let nbLivePath = null;

    // 对话框相关变量
    let chatModalResetStyle = null;
    let chatModalOpacity = 0.95;
    let containerOpacity = 0.95;
    let messageOpacity = 0.95;
    let chatInputValue = null;
    
    // ----- 聊天消息处理相关变量 -----
    let currentStreamId = null;        // 当前流式消息ID
    let currentStreamContainer = null; // 当前流式消息容器
    let lastUserMessageTime = null;    // 最后用户消息时间
    let lastMessageContent = null;     // 最后消息内容
    const STREAM_TIMEOUT = 30000;      // 流式消息超时时间（毫秒）

    // 测试模态框相关变量
    let testModalOffset = {
        x: 0,
        y: 0
    };
    let testModalSize = {
        width: 0,
        height: 0
    };
    
    let currentAudio = null;
    let isMediaPlaying = false;
    let animationFrame = null;
    let positionToggle = Math.random() > 0.5;
    let particleInterval = null;
    let currentAnimation = 'scale';
    let isAnimating = false;

    const SYSTEM_AVATAR = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%234A90E2'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3EAI%3C/text%3E%3C/svg%3E`;
    const USER_AVATAR = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23FF6B6B'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='14' font-weight='bold'%3E用%3C/text%3E%3C/svg%3E`;

    // 存储原始RGB颜色值
    let originalModalRGB = '255, 255, 255';
    let originalHeaderRGB = '74, 144, 226';
    let originalContainerRGB = '255, 255, 255';
    let originalSystemMessageRGB = '240, 240, 240';
    let originalUserMessageRGB = '74, 144, 226';
    let originalInputAreaRGB = '255, 255, 255';
    let originalChatSendButtonRGB = '74, 144, 226';
    let chatModalOffset = {
        x: 0,
        y: 0
    };
    let chatModalSize = {
        width: 0,
        height: 0
    };
    let chatModalSizeElements = {
        widthSlider: els.chatWidthSlider,
        widthValue: els.chatWidthValue,
        heightSlider: els.chatHeightSlider,
        heightValue: els.chatHeightValue
    };
    let chatModalOffsetElements = {
        xOffsetSlider: els.chatXPositionSlider,
        xOffsetValue: els.chatXPositionValue,
        yOffsetSlider: els.chatYPositionSlider,
        yOffsetValue: els.chatYPositionValue
    };

    let fontSize = 0;

    // element 元素打包
    let dhOffsetElements = {
        xOffsetSlider: els.xOffsetSlider,
        xOffsetValue: els.xOffsetValue,
        yOffsetSlider: els.yOffsetSlider,
        yOffsetValue: els.yOffsetValue
    };

    let dhSizeElements = {
        widthSlider: els.customWidthSlider,
        widthValue: els.customWidthValue,
        heightSlider: els.customHeightSlider,
        heightValue: els.customHeightValue
    };

    let dhOffsetAndSizeElements = {
        ...dhOffsetElements,
        ...dhSizeElements
    };

    // 为抠图背景色创建一个可复用的对象，避免在drawFrame中频繁创建
    let reusableBgColor = { r: 0, g: 0, b: 0 };

    //#endregion 变量定义结束
    //#region 音乐板块变量定义
    let playlist = [];
    let currentTrackIndex = -1;
    let isPlaying = false;
    let loopCounter = 0;
    let loopTimerId = null;
    let selectedItemIndex = -1; // 新增：当前选中的项目索引
    let techMediaPositionMode = 0; // 0: 随机左右, 1: 总是左, 2: 总是右，3：顶部，4：中心，5：底部，6：左右交替
    // let dhForTechMediaPositionMode = 3; // 0: 随机左右, 1: 总是左, 2: 总是右，3：左右交替
    // 适应1080 x 1886的小尺寸
    let dhSizeForTechMedia = {
        width: 300,
        height: 524
    };

    let dhMarginXForTechMedia = 80;
    let dhMarginYForTechMedia = 0;
    
    // 添加全局媒体设置
    const techMediaPlaySettings = {
        autoCloseEnabled: true,     // 是否启用自动关闭
        autoCloseOnManualUpload: false,     // 是否在手动上传时取消自动关闭
        autoCloseOnUpload: true,     // 是否在自动关闭后清空媒体框
        imageDisplayTime: 5000,     // 图片显示时间(毫秒)
        fadeOutTime: 500           // 淡出动画时间(毫秒)
    };

    // let techMediaIsClosed = true;

    // let lastUploadedUrl = null;

    // ASR 语音识别相关变量
    var isWakeUp = false; // 定义isWakeUp变量，初始为false
    // 添加唤醒状态超时变量
    let wakeWordTimeoutId = null;
    let WAKE_WORD_TIMEOUT = 180000; // 唤醒状态持续时间，60秒
    let isFileMode = false; // 是否为文件模式
    
    // 添加音频音量监测变量
    // 用户是否关闭数字人说话的录音
    let userCloseDigitalHumanSpeaking = false;
    // 用户是否关闭科技媒体播放的录音
    let userCloseTechMediaSpeaking = false;
    // var lastAudioVolumeTime = 0; // 上次检测到高音量的时间
    // var lastLowVolumeTime = 0; // 上次检测到低音量的时间
    // var audioVolumeThreshold = 20; // 音量阈值，可根据实际情况调整
    // var micMuteTimeout = null; // 麦克风静音超时计时器
    // var audioCheckInterval = null;
    // var audioContext = null;
    // var audioAnalyser = null;
    // var audioDataArray = null;
    // var isAudioMonitoring = false;
    // var isDHAudioDetecting = false;

    // 配置参数
    var micMuteDuration = 3000; // 麦克风静音持续时间(毫秒)
    var recoveryCheckInterval = 100; // 恢复检查间隔(毫秒)

    // 人脸检测状态变量
    let faceDetectionEnabled = true;          // 是否启用人脸检测
    let requireFaceVerification = false;      // 是否需要身份验证
    let faceDetectionInterval = 150;          // 检测间隔(ms)
    let faceVerificationThreshold = 0.6;      // 身份验证阈值(0-1之间，越小越严格)
    let autoSaveProfile = true;              // 是否自动保存人脸特征
    let faceVerificationTimer = null;         // 人脸验证计时器
    let faceVerificationTimeout = 5000;       // 人脸验证超时时间(毫秒)
    let isFaceVerifying = false;             // 是否正在人脸验证
    let lastFaceVerificationResult = null;    // 最后一次人脸验证结果
    let faceDetectionSendText = '';
    let currentFaceTextIsSend = false;

    let faceDetectionModuleInitialized = false; // 人脸检测模块初始化状态

    // VAD状态变量
    let vadInitialized = false;
    let vadActive = false;
    let vadPaused = false;
    let vadWaveform = null;
    let dhSpeakingVolume = 0;
    let isDigitalHumanSpeaking = false;
    let isProcessingVADResult = false;

    // VAD配置
    const vadConfig = {
        positiveSpeechThreshold: 0.8,    // 语音检测阈值（越高越严格）
        negativeSpeechThreshold: 0.8,    // 非语音检测阈值（越高越严格）
        minSpeechFrames: 4,              // 最小语音帧数
        preSpeechPadFrames: 1,           // 语音前填充帧数
        redemptionFrames: 3,             // 恢复帧数
        frameSamples: 1024,              // 每帧采样数
        
        // 麦克风配置
        additionalAudioConstraints: {
            echoCancellation: true,      // 回声消除
            noiseSuppression: true,      // 噪声抑制
            autoGainControl: true        // 自动增益控制
        }
    };

    //#endregion 变量定义结束
    
    //#region 数字人基础板块方法定义
    function start() {
        // 隐藏useCanvas2D
        els.useCanvas2DContainer.style.display = 'none';
        els.useCanvas2DInfo.style.display = 'none';
        // 确保先清理资源，避免多个连接实例
        if (pc && pc.connectionState !== 'closed') {
            console.log('检测到已有WebRTC连接，先关闭现有连接...');
            try {
                stop();
                // 等待连接完全关闭
                setTimeout(() => {
                    console.log('连接已关闭，重新开始连接...');
                    
                    startConnection();
                }, 500);
                return;
            } catch (error) {
                console.error('关闭现有连接出错:', error);
                // 继续尝试建立新连接
            }
        }
        
        // 如果没有现有连接，直接开始连接
        startConnection();
    }

    async function startConnection() {
        const config = {
            sdpSemantics: 'unified-plan'
        };

        if (els.useSTUN.checked) {
            // config.iceServers = [{
            //     urls: 'turn:**************:3478',
            //     username: 'geist',
            //     credential: 'yjh542',
            //     protocol: 'udp' 
            // }];
            config.iceServers = [{ urls: ['stun:stun.miwifi.com:3478'] }];
        }

        pc = webrtc.createPeerConnection(config);
        // 如果 pc 为空，则重新创建
        if (!pc) {
            showToast('创建连接失败或当前环境不支持 WebRTC', 'error');
            return;
        }

        // 创建一个隐藏的 video 元素用于接收视频流
        
        videoElement = document.createElement('video');
        videoElement.style.display = 'none';
        videoElement.crossOrigin = "anonymous"; // 处理跨域问题

        // 获取用户输入的宽度和高度
        // customWidth = parseInt(document.getElementById('width-input').value);
        // customHeight = parseInt(document.getElementById('height-input').value);

        // connect audio / video
        pc.addEventListener('track', (evt) => {
            if (evt.track.kind === 'video') {
                videoElement.srcObject = evt.streams[0];
                
                // 禁用视频流中的音频轨道
                // const audioTracks = evt.streams[0].getAudioTracks();
                // if (audioTracks && audioTracks.length > 0) {
                //     audioTracks.forEach(track => {
                //         track.enabled = false; // 禁用而不是停止，避免影响其他地方
                //         console.log("已禁用视频流中的音频轨道");
                //     });
                // }
                videoElement.addEventListener('canplaythrough', () => {
                    videoElement.play();

                    // 记录视频原始比例
                    if (videoRatioWidth === null && videoRatioHeight === null) {
                        // 设置局部变量
                        videoRatioWidth = videoElement.videoWidth;
                        videoRatioHeight = videoElement.videoHeight;

                        // 同时设置为全局变量，供HTML使用
                        videoRatioWidth = videoElement.videoWidth;
                        videoRatioHeight = videoElement.videoHeight;

                        // 获取视频原始比例
                        aspectRatio = videoRatioWidth / videoRatioHeight;
                        customAspectRatio = aspectRatio;

                        console.log("视频原始尺寸:", videoRatioWidth, "x", videoRatioHeight);
                    }

                    // 根据锁定比例和宽度调整高度
                    if (isRatioLocked) {
                        dhSize.height = (dhSize.width / videoRatioWidth) * videoRatioHeight;
                    }

                    // 获取容器尺寸
                    const mediaDiv = els.mediaElement;
                    const mediaDivRect = mediaDiv.getBoundingClientRect();
                    const containerWidth = mediaDivRect.width;
                    const containerHeight = mediaDivRect.height;

                    // 计算合适的canvas尺寸，确保不超出容器
                    let canvasWidth = dhSize.width && dhSize.width > 0 ? dhSize.width : videoElement.videoWidth;
                    let canvasHeight = dhSize.height && dhSize.height > 0 ? dhSize.height : videoElement.videoHeight;

                    // 获取初始宽高和初始比例给全局变量
                    initialWidth = canvasWidth;
                    initialHeight = canvasHeight;
                    customAspectRatio = canvasWidth / canvasHeight;

                    // 如果超出容器，进行缩放
                    if (canvasWidth > containerWidth * 0.9 || canvasHeight > containerHeight * 0.9) {
                        const scaleFactorW = (containerWidth * 0.9) / canvasWidth;
                        const scaleFactorH = (containerHeight * 0.9) / canvasHeight;
                        const scaleFactor = Math.min(scaleFactorW, scaleFactorH);

                        canvasWidth = Math.floor(canvasWidth * scaleFactor);
                        canvasHeight = Math.floor(canvasHeight * scaleFactor);

                        // 更新自定义尺寸，以便后续使用
                        dhSize.width = canvasWidth;
                        dhSize.height = canvasHeight;

                        // 更新滑块值
                        dhSize.width = updateSliderAndInput(els.customWidthSlider, els.customWidthValue, canvasWidth);
                        dhSize.height = updateSliderAndInput(els.customHeightSlider, els.customHeightValue, canvasHeight);
                    }

                    // 设置 canvas 尺寸
                    canvas.width = canvasWidth;
                    canvas.height = canvasHeight;

                    // 设置Canvas尺寸样式
                    canvas.style.width = canvasWidth + 'px';
                    canvas.height = canvasHeight + 'px';

                    // 居中Canvas
                    const centerX = (containerWidth - canvasWidth) / 2;
                    const centerY = (containerHeight - canvasHeight) / 2;
                    canvas.style.left = centerX + 'px';
                    canvas.style.top = centerY + 'px';

                    // 同步更新位置滑块
                    dhOffset.x = updateSliderAndInput(els.xOffsetSlider, els.xOffsetValue, centerX);
                    dhOffset.y = updateSliderAndInput(els.yOffsetSlider, els.yOffsetValue, centerY);

                    // 更新定位和大小信息
                    dhPreviousSizeAndPosition = {
                        previousWidth: canvasWidth,
                        previousHeight: canvasHeight,
                        previousLeft: centerX,
                        previousTop: centerY
                    };

                    console.log('视频流已渲染，Canvas位置：', centerX, centerY);

                    if (useCanvas2D) {
                        canvas2d.initCanvas2DContext(els.canvas);
                        currentCanvasContext = 'canvas2d';
                    } else {
                        // 在这里添加WebGL初始化代码，就在requestAnimationFrame调用之前
                        // 定义顶点着色器源码
                        const vertexShaderSource = `
                            attribute vec2 a_position;
                            attribute vec2 a_texCoord;
                            varying vec2 v_texCoord;
                            void main() {
                                gl_Position = vec4(a_position, 0, 1);
                                v_texCoord = a_texCoord;
                            }
                        `;

                        // 定义片段着色器源码
                        // 改进的片段着色器源码 - 解决边缘线问题
                        // 高精度绿幕扣除片段着色器 - 解决绿边问题
                        const fragmentShaderSource = `
                            precision highp float;
                            uniform sampler2D u_image;
                            uniform vec3 u_bgColor;
                            uniform float u_tolerance;      // 对应 similarity (相似度阈值)
                            uniform float u_antiAliasing;   // 对应 smoothness (平滑度)
                            uniform float u_spill;          // 新增：溢出控制参数，默认0.1
                            varying vec2 v_texCoord;

                            // RGB转UV色彩空间 - 关键优化点1
                            // 这比HSV转换更适合绿幕抠图，因为UV分量直接表示色度信息
                            vec2 RGBtoUV(vec3 rgb) {
                                return vec2(
                                    rgb.r * -0.169 + rgb.g * -0.331 + rgb.b * 0.5 + 0.5,
                                    rgb.r * 0.5 + rgb.g * -0.419 + rgb.b * -0.081 + 0.5
                                );
                            }

                            // 计算灰度值（用于溢出处理）
                            float getLuminance(vec3 rgb) {
                                return rgb.r * 0.2126 + rgb.g * 0.7152 + rgb.b * 0.0722;
                            }

                            void main() {
                                vec4 color = texture2D(u_image, v_texCoord);
                                
                                // 特殊处理：容差为0时直接显示原图
                                if (u_tolerance <= 0.0) {
                                    gl_FragColor = color;
                                    return;
                                }
                                
                                // 1. 计算色度距离 - 关键优化点2
                                vec2 pixelUV = RGBtoUV(color.rgb);
                                vec2 keyUV = RGBtoUV(u_bgColor);
                                vec2 chromaVec = pixelUV - keyUV;
                                
                                // 色度距离（向量长度），越相似距离越小
                                float chromaDist = sqrt(dot(chromaVec, chromaVec));
                                
                                // 2. 转换参数范围（从0-255到0-1）
                                float similarity = u_tolerance / 255.0;  // 相似度阈值
                                float smoothness = u_antiAliasing;       // 平滑度（已经是0-1范围）
                                float spill = u_spill;                   // 溢出控制
                                
                                // 3. 计算基础遮罩 - 关键优化点3
                                float baseMask = chromaDist - similarity;
                                
                                // 4. 计算最终Alpha值
                                float alpha = 1.0;
                                
                                if (baseMask <= 0.0) {
                                    // 完全匹配绿幕颜色，完全透明
                                    alpha = 0.0;
                                } else {
                                    // 使用平滑过渡，并应用指数曲线增强边缘
                                    alpha = pow(clamp(baseMask / smoothness, 0.0, 1.0), 1.5);
                                }
                                
                                // 5. 溢出处理 - 关键优化点4
                                // 用于处理前景边缘混合了绿幕颜色的情况
                                vec3 finalColor = color.rgb;
                                
                                if (spill > 0.0) {
                                    float spillVal = pow(clamp(baseMask / spill, 0.0, 1.0), 1.5);
                                    
                                    // 计算当前像素的灰度值
                                    float desat = getLuminance(color.rgb);
                                    
                                    // 混合原色和去饱和色，减少绿色溢出
                                    finalColor = mix(vec3(desat), color.rgb, spillVal);
                                }
                                
                                gl_FragColor = vec4(finalColor, alpha * color.a);
                            }
                        `;

                        // 检查是否应该使用WebGL (从UI设置中获取)
                        const webglSupported = webgl.initWebGLContext(els.canvas, vertexShaderSource, fragmentShaderSource, true);
                        currentCanvasContext = 'webgl';
                        if (!webglSupported) {
                            console.warn('WebGL不可用，将降级使用Canvas 2D API进行处理');
                            canvas2d.initCanvas2DContext(els.canvas);
                            currentCanvasContext = 'canvas2d';
                        } else {
                            console.log('WebGL初始化成功，');
                        }
                    }

                    

                    // 开始绘制视频帧
                    animationFrameId = requestAnimationFrame(drawFrame);
                });
            // 在音频流处理部分(约730行)添加：
            } else if (evt.track.kind === 'audio') {
                // 处理音频流，应用延迟
                const audioStream = evt.streams[0];
                
                // 直接使用原始音频流，不做AudioContext处理
                els.audioElement.srcObject = audioStream;
                
                // 设置音频属性，强制使用扬声器
                els.audioElement.setAttribute('playsinline', '');
                els.audioElement.setAttribute('webkit-playsinline', '');
                els.audioElement.setAttribute('x5-playsinline', '');
                els.audioElement.setAttribute('x-webkit-airplay', 'allow');
                els.audioElement.setAttribute('x5-video-player-type', 'h5');
                
                // 设置高音量，避免系统自动切换到听筒
                els.audioElement.volume = 1.0;
                
                // 尝试使用setSinkId API（如果浏览器支持）
                if (typeof els.audioElement.setSinkId === 'function') {
                    try {
                        // 将音频输出设置为扬声器
                        els.audioElement.setSinkId('');
                        console.log('已将音频输出设置为默认扬声器');
                    } catch (err) {
                        console.warn('无法设置音频输出设备:', err);
                    }
                }
                
                // 确保自动播放
                els.audioElement.play().catch(err => {
                    console.warn('自动播放失败:', err);
                });
            
                // 音频流可用后启动扬声器检测
                initDetector({
                    volumeThreshold: speakerVolumeThreshold,
                    speakingHoldTime: speakerHoldTime,
                    onSpeakingStart: (volume) => {
                        console.log(`数字人开始说话 (${volume}%)`);
                        dhSpeakingVolume = volume;
                        // 如果正在录音且未暂停，则暂停录音
                        if (isRecording && !isRecordingPaused && speakerDetectionEnabled && !userCloseDigitalHumanSpeaking) {
                            try {
                                pauseRecording();
                                // 暂停语音活动检测
                                pauseVADIfActive();
                                wasPausedBySpeaker = true;
                                isDigitalHumanSpeaking = true;
                                updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 检测到数字人正在说话，录音已暂停</span>`);
                            } catch (error) {
                                console.error('暂停录音失败:', error);
                            }
                        }
                    },
                    onSpeakingEnd: () => {
                        console.log('数字人停止说话');
                        
                        // 如果录音是由扬声器检测暂停的，则恢复录音
                        if (isRecording && isRecordingPaused && wasPausedBySpeaker && speakerDetectionEnabled && !userCloseDigitalHumanSpeaking) {
                            try {
                                resumeRecording();
                                // 恢复语音活动检测
                                resumeVADIfPaused();
                                wasPausedBySpeaker = false;
                                isDigitalHumanSpeaking = false;
                                updateASRStatusHTML(`<span style="color:#06b6d4">✓ 数字人停止说话，录音已恢复</span>`);
                            } catch (error) {
                                console.error('恢复录音失败:', error);
                            }
                        }
                    },
                    onSpeaking: (volume) => {
                        dhSpeakingVolume = volume;
                        // console.log('检测到数字人说话声音:', volume);
                        // 如果正在录音且未暂停，则暂停录音
                        if (isRecording && !isRecordingPaused && speakerDetectionEnabled && !userCloseDigitalHumanSpeaking) {
                            try {
                                pauseRecording();
                                // 暂停语音活动检测
                                pauseVADIfActive();
                                wasPausedBySpeaker = true;
                                // isDigitalHumanSpeaking = true; 没必要加
                                updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 检测到数字人正在说话，录音已暂停</span>`);
                            } catch (error) {
                                console.error('暂停录音失败:', error);
                            }
                        }
                    },
                    onError: (error) => {
                        console.error('音频检测错误:', error);
                    }
                });

                startDetection(els.audioElement)
                    .then(() => {
                        console.log('开始对扬声器进行检测');
                    })
                    .catch(err => {
                        console.error('启动扬声器检测失败:', err);
                    });
            }
        });

        els.startButton.style.display = 'none';

        els.stopButton.style.display = 'inline-block';

        await negotiate();

        // 更新数字人是否启动完成的变量
        isDigitalHumanStarted = true;

        // 显示canvas
        els.canvas.classList.add('show');
        
        getDHPersonaText();

        // 添加系统消息
        addSystemMessage('你好，我是小幸运，很高兴为你服务。有什么可以帮助你的吗？可以随时向我提问哦。');

        // 连接DHWS
        // try {
        //     await connectDigitalHuman();
        // } catch (error) {
        //     console.error('连接DHWS失败:', error);
        //     showToast('连接DHWS失败: ' + error.message, 'error');
        // }

        // 连接OC服务器
        // try {
        //     await connectOCServer();
        // } catch (error) {
        //     console.error('连接OC服务器失败:', error);
        //     showToast('连接OC服务器失败: ' + error.message, 'error');
        // }

        // 连接ASR
        // try {
        //     await connectASRServer();

        //     // 自动启动VAD
        //     toggleVAD();
        // } catch (error) {
        //     console.error('连接ASR失败:', error);
        //     showToast('连接ASR失败: ' + error.message, 'error');
        // }

    }


    //====================================================================================================
    startSpeakingCheck(); // 启动说话状态检查

    // 在页面加载时取消音频和视频的静音状态
    toggleMute(false);

    // 设置初始按钮背景图片
    btnButtonCall.style.backgroundImage = image_call;

    // 为"呼叫"按钮添加点击事件监听器
    btnButtonCall.addEventListener("click", function (event) {
        if (btnButtonCall.style.backgroundImage.includes("phone_call")) {
            toggleMute(false); // 取消静音
            // show_click_message = false; // 只显示一次点击消息
            btnButtonCall.style.backgroundImage = image_down; // 改变按钮图片为挂断电话
            
            
            // 检查人脸检测复选框状态
            const faceDetectionToggle = document.getElementById('face-detection-toggle');
            if (faceDetectionToggle && faceDetectionToggle.checked) {
                // 如果人脸检测被选中，先开启人脸检测
                console.log('人脸检测已启用，开始人脸检测流程');
                handleFaceDetectionFlow();
            } else {
                // 如果人脸检测未选中，继续之前的唤醒词检查
                console.log('人脸检测未启用，开始录音进行唤醒词检查');
                toggleVAD(); // 启动VAD
                start_record(); // 开始录音
            }

        } else {
            btnButtonCall.style.backgroundImage = image_call; // 改变按钮图片为呼叫电话

            // 关闭人脸检测（如果正在进行）
            if (isFaceVerifying) {
                closeFaceDetection();
                console.log('已关闭人脸检测');
            }

            record_stop(); // 停止录音
            toggleMute(true); // 设置静音
        }
    });


    /**
     * 处理人脸检测流程
     * 循环检测人脸，直到检测到人脸后进入唤醒词检查
     */
    async function handleFaceDetectionFlow() {
        try {
            // 初始化人脸检测模块（如果尚未初始化）
            if (!faceDetectionModuleInitialized) {
                await initializeFaceDetectionModule();
            }

            // 更新状态显示
            updateASRStatusHTML(`<span style="color:#06b6d4">⏱️ 正在进行人脸检测，请看向摄像头...</span>`);

            // 开启人脸检测
            const faceDetectionStarted = await openFaceDetection();

            if (!faceDetectionStarted) {
                // 人脸检测启动失败，回退到唤醒词检查
                console.log('人脸检测启动失败，回退到唤醒词检查');
                updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 人脸检测启动失败</span>`);
                return;
            }

            console.log('人脸检测已启动，等待检测到人脸...');

        } catch (error) {
            console.error('人脸检测流程出错:', error);
            // 出错时回退到唤醒词检查
            updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 人脸检测出错</span>`);

        }
    }

    /**
     * 强制重置录音系统
     */
    function forceResetRecordingSystem() {
        console.log('=== 强制重置录音系统 ===');

        // 停止所有录音相关活动
        if (typeof window.record_stop === 'function') {
            window.record_stop();
        }

        // 关闭麦克风资源
        if (typeof window.closeMicrophone === 'function') {
            window.closeMicrophone();
        }

        // 重置录音器状态变量
        if (typeof window.RecordingState !== 'undefined') {
            window.recordingState = window.RecordingState.STOPPED;
        }

        // 重置其他状态变量
        if (typeof window.isRec !== 'undefined') {
            window.isRec = false;
        }
        if (typeof window.isRecordingPaused !== 'undefined') {
            window.isRecordingPaused = false;
        }
        if (typeof window.recorderOpened !== 'undefined') {
            window.recorderOpened = false;
        }

        console.log('录音系统状态已强制重置');
    }

    /**
     * 安全地调用 start_record 函数
     */
    function safeStartRecord() {
        console.log('=== 尝试启动录音 ===');

        // 检查录音器状态
        if (typeof window.getRecordingState === 'function') {
            console.log('当前录音状态:', window.getRecordingState());
        }

        // 强制重置录音系统
        forceResetRecordingSystem();

        // 延迟一段时间确保状态重置
        setTimeout(() => {
            console.log('开始重新初始化录音系统...');

            // 重新请求麦克风权限
            if (typeof window.requestMicrophoneAccess === 'function') {
                console.log('重新请求麦克风权限...');
                window.requestMicrophoneAccess()
                    .then(() => {
                        console.log('麦克风权限获取成功，开始初始化录音器');

                        // 重新初始化录音器
                        if (typeof window.initRecorder === 'function') {
                            console.log('重新初始化录音器...');
                            window.initRecorder();
                        }

                        // 延迟后启动录音
                        setTimeout(() => {
                            console.log('准备启动正式录音...');
                            if (typeof window.start_record === 'function') {
                                console.log('调用 start_record 函数');
                                window.start_record();

                                // 检查启动后的状态
                                setTimeout(() => {
                                    if (typeof window.getRecordingState === 'function') {
                                        console.log('启动后录音状态:', window.getRecordingState());
                                    }
                                }, 1000);
                            } else {
                                console.error('start_record 函数未定义');
                            }
                        }, 500);
                    })
                    .catch((error) => {
                        console.error('重新获取麦克风权限失败:', error);
                        // 即使权限获取失败，也尝试启动录音
                        setTimeout(() => {
                            if (typeof window.start_record === 'function') {
                                window.start_record();
                            }
                        }, 500);
                    });
            } else {
                // 如果没有权限请求函数，直接尝试启动
                setTimeout(() => {
                    if (typeof window.start_record === 'function') {
                        console.log('直接调用 start_record 函数');
                        window.start_record();
                    }
                }, 500);
            }
        }, 500);
    }

    // 添加控制音频静音的函数
    function toggleMute(isMuted) {
        if (videoElement) {
            videoElement.muted = isMuted;
            console.log(`音频和视频已${isMuted ? "静音" : "取消静音"}`);
        } else {
            console.log(`videoElement 未初始化，无法${isMuted ? "静音" : "取消静音"}`);
        }
    }
    


	// 将 message_get 函数暴露为全局函数
	window.message_get = async function() {
		var message = $("#message").val();
		
		console.log("Sending: " + message);
		console.log("sessionid: ", document.getElementById("sessionid").value);

		// 获取提交按钮并显示分析状态
		const submitBtn = document.getElementById("btnSubmit");
		const originalText = submitBtn.textContent;
		submitBtn.textContent = "分析中...";
		submitBtn.classList.add("analyzing");
		submitBtn.disabled = true;

		try {
			// 检查摄像头是否启动
			if (
				userStream &&
				userCamera &&
				userCamera.videoWidth &&
				userCamera.videoHeight
			) {
				// 摄像头已启动，进行视觉识别
				console.log("摄像头已启动，进行视觉识别");
				const visionDescription = await performVisionAnalysisWithMessage(message);
			} else {
				// 摄像头未启动，直接发送消息
				console.log("摄像头未启动，直接发送消息");
				message_send(message);
			}

			// 清空输入框
			$("#message").val("");
		} catch (error) {
			console.error("处理消息时出错:", error);
			message_send(message);
			$("#message").val("");
		} finally {
			// 恢复按钮状态
			submitBtn.textContent = originalText;
			submitBtn.classList.remove("analyzing");
			submitBtn.disabled = false;
		}
	};


	// 新增函数：带消息参数的视觉识别
	window.performVisionAnalysisWithMessage = async function(message) {
	  return new Promise(async (resolve, reject) => {
        
		// if (!userStream || !userCamera.videoWidth || !userCamera.videoHeight) {
		//   message_send(message);
		//   console.log("摄像头未开启，跳过视觉识别");
		//   resolve(null);
		//   return;
		// }


		// 使用 TRIGGER_KEYWORDS 数组中的所有关键词进行过滤
		// let filteredMessage = message;
		// TRIGGER_KEYWORDS.forEach((keyword) => {
		//   filteredMessage = filteredMessage.replace(new RegExp(keyword, "g"), "");
		// });

		addUserMessage(message);
		// 创建canvas捕获当前帧
		const canvas = document.createElement("canvas");
		const ctx = canvas.getContext("2d");
		canvas.width = userCamera.videoWidth;
		canvas.height = userCamera.videoHeight;

		// 绘制当前视频帧到canvas
		ctx.drawImage(userCamera, 0, 0, canvas.width, canvas.height);

		// 转换为base64图片数据
		const imageData = canvas.toDataURL("image/jpeg", 0.8);

		fetch("/human", {
		  body: JSON.stringify({
			interrupt: true,
			type: "vision",
			image: imageData,
			text: message, // 传递用户输入的消息
			sessionid: document.getElementById("sessionid").value,
		  }),
		  headers: {
			"Content-Type": "application/json",
		  },
		  method: "POST",
		  // cache: "no-store",
		})
		.then(response => response.json())
		.then(data => {
	  
			// console.log('Received chat response:', data);
	  
			if (data.code === 0 && data.data) {
	  
				if(data.type=='text'){
					addSystemMessage(data.data);
				}else if(data.type=='video'){
					showMediaInTechPlayerHandler(data);
				}else if(data.type=='music'){
					addBackgroundMusic(data);
				}else{
					addSystemMessage(data.data);
				}
				resolve(data.data);
			}else{
				addSystemMessage('消息发送失败，请重试');
				resolve(null);
			}
			
		})
		.catch(error => {
			console.error('请求发生错误:', error);
			// 可以在聊天窗口中添加错误提示
			addSystemMessage('网络错误，或数字人未开启，请检查连接');
			// 解决Promise
			resolve(null);
		});

	  });
	}

	window.message_send = async function(message) {

	  // 使用 TRIGGER_KEYWORDS 数组中的所有关键词进行过滤
	//   let filteredMessage = message;
	//   TRIGGER_KEYWORDS.forEach((keyword) => {
	// 	filteredMessage = filteredMessage.replace(new RegExp(keyword, "g"), "");
	//   });

	  addUserMessage(message);
	  fetch("/human", {
		body: JSON.stringify({
		  text: message,
		  type: "chat",
		  interrupt: true,
		  sessionid: parseInt(document.getElementById("sessionid").value),
		}),
		headers: {
		  "Content-Type": "application/json",
		},
		method: "POST",
		cache: "no-store",
	  })
	  .then(response => response.json())
	  .then(data => {

		  console.log('Received chat response:', data);

		  if (data.code === 0 && data.data) {

			  if(data.type=='text'){
				  addSystemMessage(data.data);
			  }else if(data.type=='video'){
				  showMediaInTechPlayerHandler(data);
			  }else if(data.type=='music'){
				  addBackgroundMusic(data);
			  }else{
				  addSystemMessage(data.data);
			  }

		  }else{
			  addSystemMessage('消息发送失败，请重试');
		  }
		  
		  
	  })
	  .catch(error => {
		  console.error('请求发生错误:', error);
		  // 可以在聊天窗口中添加错误提示
		  addSystemMessage('网络错误，或数字人未开启，请检查连接');
	  });
	  
	}
    //====================================================================================================


    // 在main.js中找到negotiate函数(约849行)，修改为：
    async function negotiate() {
        try {
            // 使用更明确的配置
            pc.addTransceiver('video', { 
                direction: 'recvonly',
                streams: [] // 确保不包含音频
            });
            
            pc.addTransceiver('audio', { 
                direction: 'recvonly'
            });
            
            // 等待 createOfferAndSetLocalDescription 完成
            await webrtc.createOfferAndSetLocalDescription(pc);
            
            // 添加比特率限制
            // if (pc.localDescription && pc.localDescription.sdp) {
            //     let sdp = pc.localDescription.sdp;
                
            //     // 为视频流添加比特率限制 (400 kbps)
            //     sdp = sdp.replace(/m=video.*\r\n/g, (match) => {
            //         return match + 'b=AS:400\r\n';
            //     });
                
            //     // 为音频流添加比特率限制 (64 kbps)
            //     sdp = sdp.replace(/m=audio.*\r\n/g, (match) => {
            //         return match + 'b=AS:64\r\n';
            //     });
                
            //     // 更新本地描述
            //     const newDesc = new RTCSessionDescription({
            //         type: pc.localDescription.type,
            //         sdp: sdp
            //     });
                
            //     await pc.setLocalDescription(newDesc);
            //     console.log("新的SDP:", sdp);
            //     showToast("已设置比特率限制: 视频 400kbps, 音频 64kbps", "success");
            //     console.log("已设置比特率限制: 视频 400kbps, 音频 64kbps");
            // }
            
            if (pc.signalingState !== "closed") {
                // 等待 exchangeDescriptions 完成
                const answer = await webrtc.exchangeDescriptions(pc, pc.localDescription, '/offer');
                // 发送过去的SDP
                console.log("发送过去的SDP:", pc.localDescription);
                els.sessionId.value = answer.sessionid;
                
                // 记录SDP以便调试
                console.log("SDP Answer:", answer);
            }
        } catch (e) {
            alert(e);
        }
    }

    function stop() {

        // 如果数字人未启动完成，则不停止
        if (!isDigitalHumanStarted) {
            return;
        }

        // 取消下一个动画帧
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }

        els.stopButton.style.display = 'none';
        // 显示 start 按钮
        els.startButton.style.display = 'block';

        // 显示 useCanvas2DContainer
        els.useCanvas2DContainer.style.display = 'flex';
        // 显示 useCanvas2DInfo
        els.useCanvas2DInfo.style.display = 'block';

        // 清空 canvasWebGL
        if (webgl.gl) {
            // 使用正确的 WebGL 清除方法
            webgl.gl.clearColor(0.0, 0.0, 0.0, 0.0); // 设置清除颜色为透明
            webgl.gl.clear(webgl.gl.COLOR_BUFFER_BIT); // 清除颜色缓冲区
        }

        // 清空 canvas2d
        canvas2d.clearCanvas2D();

        // 停止并隐藏 videoElement
        if (videoElement) {
            videoElement.pause();
            videoElement.srcObject = null;
            videoElement.style.display = 'none';
        }

        // 隐藏canvas
        els.canvas.classList.remove('show');

        // 停止扬声器检测
        stopDetection();

        // 关闭WebRTC连接
        webrtc.closeConnection(pc, els.videoElement, els.audioElement);

        try {
            disconnectAllConnections();
        } catch (error) {
            console.error('断开所有连接时出错:', error);
        }
        
        console.log('所有资源已清理完毕');

        // 更新数字人是否启动完成的变量
        isDigitalHumanStarted = false;
    }

    function drawFrame() {
        // 1. 检查视频状态，如果暂停或结束则停止循环
        if (!videoElement || videoElement.paused || videoElement.ended) {
            // 如果有动画帧ID，取消它，确保完全停止
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            return;
        }
    
        // --- 性能监控部分 (保持不变) ---
        const startTime = performance.now();
        
        if (window.limitFPS) {
            const now = performance.now();
            const elapsed = now - (window.lastFrameTime || 0);
            const frameInterval = 1000 / (window.targetFPS || 30);
            
            if (elapsed < frameInterval) {
                animationFrameId = setTimeout(() => requestAnimationFrame(drawFrame), frameInterval - elapsed);
                return;
            }
            
            window.lastFrameTime = now;
        }
        
        frameCount++;
        const now = performance.now();
        
        if (now - lastFpsUpdateTime >= fpsUpdateInterval) {
            currentFps = Math.round((frameCount * 1000) / (now - lastFpsUpdateTime));
            frameCount = 0;
            lastFpsUpdateTime = now;
            
            const performanceStats = document.getElementById('performance-stats');
            if (performanceStats) {
                performanceStats.textContent = `FPS: ${currentFps} | 帧处理时间: ${frameProcessingTime.toFixed(1)}ms`;
            }
            
            if (frameProcessingTime > 30 && !window.limitFPS) {
                console.log("检测到性能问题，启用性能优化模式");
                window.limitFPS = true;
                window.targetFPS = 20;
            }
        }
    
        const canvas = els.canvas;
        
        // --- 尺寸计算部分 (保持不变) ---
        const UPDATE_SIZE_INTERVAL = 500;
        if (!drawFrame.lastSizeUpdateTime || now - drawFrame.lastSizeUpdateTime > UPDATE_SIZE_INTERVAL) {
            if (isRatioLocked && customAspectRatio) {
                dhSize.height = Math.round(dhSize.width / customAspectRatio);
            }
            
            const displayWidth = dhSize.width && dhSize.width > 0 ? dhSize.width : videoElement.videoWidth;
            const displayHeight = dhSize.height && dhSize.height > 0 ? dhSize.height : videoElement.videoHeight;
            
            let renderWidth = displayWidth;
            let renderHeight = displayHeight;
            
            if (renderWidth > maxCanvasResolution || renderHeight > maxCanvasResolution) {
                const scaleFactor = maxCanvasResolution / Math.max(renderWidth, renderHeight);
                renderWidth = Math.floor(renderWidth * scaleFactor);
                renderHeight = Math.floor(renderHeight * scaleFactor);
            }
            
            const sizeChanged = (canvas.width !== renderWidth || 
                                canvas.height !== renderHeight || 
                                parseInt(canvas.style.width) !== displayWidth || 
                                parseInt(canvas.style.height) !== displayHeight);
            
            if (sizeChanged) {
                canvas.width = renderWidth;
                canvas.height = renderHeight;
                canvas.style.width = displayWidth + 'px';
                canvas.style.height = displayHeight + 'px';
                centerCanvasInContainer();
                drawFrame.lastSizeUpdateTime = now;
            }
        }
        
        // --- 绿幕颜色处理部分 (核心改造点) ---
    
        // 2. 首帧检测背景颜色
        if (isFirstFrame) {
            // 创建一个临时的canvas来分析首帧，这部分开销只在启动时有一次，可以接受。
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = canvas.width;
            tempCanvas.height = canvas.height;
            const tempCtx = tempCanvas.getContext('2d');
            tempCtx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
            
            // detectDominantColor 应该返回一个新对象
            const detectedColor = detectDominantColor(tempCtx, canvas.width, canvas.height);
            
            // 将检测到的颜色值赋给我们的可复用对象
            reusableBgColor.r = detectedColor.r;
            reusableBgColor.g = detectedColor.g;
            reusableBgColor.b = detectedColor.b;
    
            // 更新UI
            inputBgColorHex = rgbToHex(reusableBgColor.r, reusableBgColor.g, reusableBgColor.b);
            els.bgColorPicker.value = inputBgColorHex;
            els.bgColorValue.value = inputBgColorHex;
            
            showToast(`自动抠图成功，背景颜色为：${inputBgColorHex}`, "success");
            isFirstFrame = false;
        }
        
        // 3. ✨ 核心优化：只在颜色变化时更新可复用对象，避免每帧创建新对象
        //    我们将bgColorRGB这个临时变量彻底移除，全程使用 reusableBgColor
        if (inputBgColorHex !== drawFrame.lastBgColorHex) {
            // 调用修改后的hexToRgb，它会修改reusableBgColor的内部值，而不是创建新对象
            hexToRgb(inputBgColorHex, reusableBgColor); 
            drawFrame.lastBgColorHex = inputBgColorHex;
        }
    
        // --- 渲染部分 ---
    
        // 4. 将可复用的颜色对象传递给渲染函数
        if (currentCanvasContext === 'webgl') {
            webgl.drawFrameWebGL(videoElement, canvas.width, canvas.height, reusableBgColor, tolerance, antiAliasingStrength, spill);
        } else if (currentCanvasContext === 'canvas2d') {
            canvas2d.drawFrameCanvas2D(videoElement, canvas.width, canvas.height, reusableBgColor, tolerance, antiAliasingStrength, spill);
        }
    
        // --- 循环与计时部分 ---
        frameProcessingTime = performance.now() - startTime;
    
        // 5. 继续下一帧
        animationFrameId = requestAnimationFrame(drawFrame);
    }

    async function connectDigitalHuman() {
        if (!els.sessionId.value) {
            console.error('缺少sessionId,无法创建数字人连接');
            return;
        }
    
        try {
            if (!dhConnection) {
                dhConnection = createConnection(CONNECTION_TYPES.DIGITAL_HUMAN, {
                    debug: true,
                    onConnected: (data) => {
                        console.log('数字人连接成功:', data);
                    },
                    onDisconnected: (data) => {
                        console.log('数字人连接断开:', data);
                    },
                    onError: (error) => {
                        console.error('数字人连接错误:', error);
                    },
                    onMessage: (data) => {
                        console.log('收到数字人消息:', data);
                        // 添加数字人消息到聊天窗口
                        const messageData = data.data;
                        if (messageData.type === 'text' && messageData.url) {
                            // 检查是否是流式消息的开始
                            handleSystemMessageStream(messageData.url);
                        } else if (messageData.type === 'video' && messageData.url) {
                            // 如果 window.protocol 是 https将 messageData.url http 开头转为 以 https 开头
                            if (window.protocol === 'https') {
                                messageData.url = messageData.url.replace('http://video.s.odn.cc/', `https://${window.host}/`);
                            }
                            // 先关闭所有的模态框
                            closeMediaFrameAndResetDH();
                            // 显示视频到媒体框
                            const videoElement = displayAndPlayVideo(messageData.url, els.mediaContent);
                            console.log('视频元素:', videoElement);
                            // 打开媒体框
                            openMediaFrameAndPositionDH();
                            closeAllModals();
                            // 检测视频声音
                            startMediaSpeakerDetection(videoElement);
                        } else if (messageData.type === 'image' && messageData.url) {
                            // 如果 window.protocol 是 https将 messageData.url http 开头转为 以 https 开头
                            if (window.protocol === 'https') {
                                messageData.url = messageData.url.replace('http://video.s.odn.cc/', `https://${window.host}/`);
                            }
                            // 先关闭所有的模态框
                            closeMediaFrameAndResetDH();
                            displayImage(messageData.url, els.mediaContent);
                            // 打开媒体框
                            openMediaFrameAndPositionDH();
                            closeAllModals();
                        } else if (messageData.type === 'music' && messageData.url) {
                            // 如果 window.protocol 是 https将 messageData.url http 开头转为 以 https 开头
                            if (window.protocol === 'https') {
                                messageData.url = messageData.url.replace('http://video.s.odn.cc/', `https://${window.host}/`);
                            }
                            // 先关闭所有的模态框
                            closeMediaFrameAndResetDH();
                            // 改用现有的方法
                            const audioElement = displayAndPlayAudio(messageData.url, els.mediaContent);
                            console.log('音频元素:', audioElement);
                            // 打开媒体框
                            openMediaFrameAndPositionDH();
                            closeAllModals();
                            startMediaSpeakerDetection(audioElement);
                        } else if (messageData.type === 'ppt' && messageData.url) {
                            // 打开观众窗口
                            // displayIframeWPSPPT(messageData.url);
                        } else if (messageData.type === 'cmd' && messageData.url) {

                        }
                    }
                });
            }
    
            const url = `${window.wsProtocol}://${window.host}/ws_ai?sessionid=${parseInt(els.sessionId.value)}`;
            await dhConnection.connect(url);
        } catch (error) {
            console.error('创建数字人连接失败:', error);
            throw error;
        }
    }

    // 创建远程控制WebSocket连接
    async function connectOCServer() {
        try {
            if (!ocConnection) {
                ocConnection = createConnection(CONNECTION_TYPES.REMOTE_CONTROL, {
                    debug: true,
                    onConnecting: (data) => {
                        console.log('远程控制连接中:', data);
                        updateOCStatusHTML('<span style="color: #2ec4b6;">正在连接远程控制服务器...</span>');
                    },
                    onConnected: (data) => {
                        console.log('远程控制连接成功:', data);
                        updateOCStatusHTML('<span style="color: green;">远程控制连接成功...</span>');
                    },
                    onDisconnected: (data) => {
                        console.log('远程控制连接断开:', data);
                        els.connectWSButton.style.display = 'block';
                        els.disconWsButton.style.display = 'none';
                        updateOCStatusHTML('<span style="color: red;">远程控制连接断开...</span>');
                    },
                    onError: (error) => {
                        console.error('远程控制连接错误:', error);
                        updateOCStatusHTML('<span style="color: red;">远程控制连接错误...</span>');
                    },
                    onMessage: async (data) => {
                        console.log('收到远程控制消息:', data);
                        const ocMessageData = data.data;
                        console.log('远程控制消息数据:', ocMessageData);
                        // updateOCStatusHTML(`<span style="color: blue;">收到远控消息: ${data.data.message}</span>`);
                        if (ocMessageData.type === 'connected') {
                            const clientId = ocMessageData.client_id;
                            console.log('远程控制客户端ID:', clientId);
                            // 更新UI
                            els.connectWSButton.style.display = 'none';
                            els.disconWsButton.style.display = 'block';
                            updateOCStatusHTML(`<span style="color: blue;">本机远控ID: <p style="margin: 0px;">${clientId}</p></span>`);
                        } else if (ocMessageData.type === 'disconnecting') {
                            // addMessage('系统', '正在断开与服务器的连接...', 'system');
                            els.statusElement.textContent = '正在断开与服务器的连接...';
                        } else if (ocMessageData.type === 'message') {
                            console.log('收到远程控制文本消息:', ocMessageData);
                            // 处理消息
                            let newType = messageType;
                            if (ocMessageData.new_type === 'echo' || ocMessageData.new_type === 'chat') {
                                newType = ocMessageData.new_type;
                                // addMessage(`用户 ${data.user_id}`, data.message, 'user-message');
                                // statusElement.textContent = `用户yi ${data.user_id}: ${data.message}`;
                
                                // 发送消息到服务器
                                console.log('向服务端发送消息:', ocMessageData.message);
                                const res = await handleMessageSend(ocMessageData.message, newType, true);
                            } else if (ocMessageData.new_type === 'video') {
                                if (window.protocol === 'https') {
                                    ocMessageData.message = ocMessageData.message.replace('http://**************:8000/', `https://${window.host}/`);
                                }
                                // 先关闭所有的模态框
                                closeMediaFrameAndResetDH();
                                // 显示视频到媒体框
                                const videoElement = displayAndPlayVideo(ocMessageData.message, els.mediaContent);
                                console.log('视频元素:', videoElement);
                                // 打开媒体框
                                openMediaFrameAndPositionDH();
                                closeAllModals();
                                // 检测视频声音
                                startMediaSpeakerDetection(videoElement);
                            } else if (ocMessageData.new_type === 'image') {
                                // 如果 window.protocol 是 https将 messageData.url http 开头转为 以 https 开头
                                if (window.protocol === 'https') {
                                    ocMessageData.message = ocMessageData.message.replace('http://**************:8000/', `https://${window.host}/`);
                                }
                                // 先关闭所有的模态框
                                closeMediaFrameAndResetDH();
                                displayImage(ocMessageData.message, els.mediaContent);
                                // 打开媒体框
                                openMediaFrameAndPositionDH();
                                closeAllModals();
                            } else if (ocMessageData.new_type === 'music' || ocMessageData.new_type === 'audio') {
                                // 如果 window.protocol 是 https将 messageData.url http 开头转为 以 https 开头
                                if (window.protocol === 'https') {
                                    ocMessageData.message = ocMessageData.message.replace('http://**************:8000/', `https://${window.host}/`);
                                }
                                // 先关闭所有的模态框
                                closeMediaFrameAndResetDH();
                                // 改用现有的方法
                                const audioElement = displayAndPlayAudio(ocMessageData.message, els.mediaContent);
                                console.log('音频元素:', audioElement);
                                // 打开媒体框
                                openMediaFrameAndPositionDH();
                                closeAllModals();
                                startMediaSpeakerDetection(audioElement);
                            }
                        }
                    }
                });
            }

            const url = `${window.wsProtocol}://${window.ocHost}/ws/client`;
            await ocConnection.connect(url);
    } catch (error) {
        console.error('创建远程控制连接失败:', error);
        updateOCStatusHTML('<span style="color: red;">创建远程控制连接失败...</span>');
            throw error;
        }
    }

    // 创建ASR WebSocket连接
    async function connectASRServer() {
        try {
            if (!asrConnection) {
                asrConnection = createConnection(CONNECTION_TYPES.SPEECH_RECOGNITION, {
                    debug: true,
                    onConnecting: (data) => {
                        console.log('ASR连接中:', data);
                        updateASRStatusHTML('<span style="color: #2ec4b6;">正在连接ASR服务器...</span>');
                    },
                    onConnected: (data) => {
                        console.log('ASR连接成功:', data);
                        updateASRStatusHTML('<span style="color: green;">ASR 语音识别服务连接成功...</span>');
                        
                        // 发送初始化请求
                        var chunk_size = new Array(5, 10, 5);
                        var request = {
                            "chunk_size": chunk_size,
                            "wav_name": "h5",
                            "is_speaking": true,
                            "chunk_interval": 10,
                            "itn": getUseITN(),
                            "mode": getAsrMode(),
                        };
                        
                        // 处理文件模式
                        if (isFileMode) {
                            request.wav_format = file_ext;
                            if (file_ext == "wav") {
                                request.wav_format = "PCM";
                                request.audio_fs = file_sample_rate;
                            }
                        }
                        
                        // 热词处理
                        const hotwords = getHotwordsJson();
                        if (hotwords != null) {
                            request.hotwords = hotwords;
                        }
                        

                        asrConnection.send(JSON.stringify(request));
                        
                        // 连接启动ASR录音
                        // try {
                        //     // 如果是移动端，则不自动开启录音
                        //     if (window.innerWidth > 768) {
                        //         startASRRecording();
                        //     }
                        // } catch (error) {
                        //     console.error('启动ASR录音失败:', error);
                        //     showToast('启动ASR录音失败: ' + error.message, 'error');
                        // }
                    },
                    onDisconnected: (data) => {
                        console.log('ASR连接断开:', data);
                        updateASRStatusHTML('<span style="color: red;">ASR连接断开...</span>');
                    },
                    onError: (error) => {
                        console.error('ASR连接错误:', error);
                        updateASRStatusHTML(`<span style="color: red;">ASR连接错误: ${error.error || '未知错误'}</span>`);
                        
                        // 原wsconnecter.js中的错误处理
                        info_div.innerHTML = '<span style="color:#f72585">❌ 连接错误: ' + (error.error || '未知错误') + '</span>';
                        
                        // 在移动设备上添加重试消息
                        if (/Android|iPhone|iPad|iPod/i.test(navigator.userAgent)) {
                            setTimeout(function() {
                                info_div.innerHTML = '<span style="color:#ff9f1c">点击"连接"按钮重新尝试连接</span>';
                                
                                // 重置按钮状态
                                var btnConnect = document.getElementById('btnConnect');
                                var btnStart = document.getElementById('btnStart');
                                var btnStop = document.getElementById('btnStop');
                                
                                if (btnConnect) btnConnect.disabled = false;
                                if (btnStart) btnStart.disabled = true;
                                if (btnStop) btnStop.disabled = true;
                            }, 2000);
                        }
                    },
                    onMessage: (data) => {
                        // 处理从服务器接收的消息，也就是ASR的识别结果
                        // 旧代码：msgHandle();
                        
                        const asrData = data.data;

                        // 使用新的ASR模块处理消息
                        const result = processASRMessage(asrData);

                        if (result) {
                            // 1. 立即更新识别结果显示（没有延迟）
                            els.asrResultArea.value = result.fullText || result.text || '';
                            // 2. 实时输出识别中的结果（立即反馈）
                            els.chatInput.value = result.fullText || result.text || '';
                            currentASRText = result.fullText || result.text || '';
                            currentASRText = currentASRText.replace(/^[\.,。，？！、：；""\s]+/g, '');
                            
                            // 2. 实时输出识别中的结果（立即反馈）
                            console.log('ASR实时识别结果:', currentASRText);
                            
                            // 3. 用户可能想看到识别到的唤醒词立即高亮显示
                            if (result.wakeWord) {
                                // 立即视觉反馈，但不执行动作
                                updateASRStatusHTML(`<span style="color:#06b6d4">检测到唤醒词: ${result.wakeWord}</span>`);
                            }
                            
                            // 4. 重置延迟发送逻辑的定时器
                            if (asrSentenceTimeoutId) {
                                clearTimeout(asrSentenceTimeoutId);
                            }
                            
                            // 5. 设置新的延迟发送定时器（只影响发送到后端的部分）
                            asrSentenceTimeoutId = setTimeout(() => {
                                console.log('开始处理最终结果:', currentASRText);
                                console.log('结果时间戳:', result.timestamp);
                                
                                // 这部分是延迟执行的（向后端发送消息）
                                handleFinalASRResult(currentASRText, {
                                    timestamp: result.timestamp,
                                    mode: result.mode
                                }, async (asrData) => {
                                    console.log('ASR结果处理成功:', asrData);
                                    
                                    // 判断结果中是否含有唤醒词
                                    if (result.wakeWord) {
                                        console.log('检测到唤醒词:', result.wakeWord.word);
                                        
                                        updateASRStatusHTML(`<span style="color:#06b6d4">✓ 已响应唤醒词: ${result.wakeWord.word}</span>`);
                                        showToast('唤醒词触发: ' + result.wakeWord.word, 'success');
                                        
                                        // 只有当未唤醒状态时，才发送唤醒词对应的招呼语
                                        if (!isWakeUp) {
                                            
                                            // 启动人脸检测循环验证
                                            if (isDigitalHumanStarted) {
                                                if (faceDetectionEnabled) {
                                                    openFaceDetection();
                                                } else {
                                                    // 直接唤醒
                                                    isWakeUp = true;
                                                    // 直接发包含唤醒词的原句
                                                    wakeupDetectionHandler(result.text);
                                                }
                                            } else {
                                                updateASRStatusHTML(`<span style="color:#ef4444">❌ 数字人未启动完成，请先启动数字人</span>`);
                                            }

                                            // 设置为唤醒状态，被搬到initFaceDetectionModule()中
                                            
                                            // 发送唤醒词对应的内容到后端
                                            // const res = await handleMessageSend(result.wakeWord.value + '，' + result.wakeWord.word);
                                            // 或者直接发送用户原话
                                            faceDetectionSendText = result.text;
                                            currentFaceTextIsSend = false;
                                        } else {
                                            // 已经在唤醒状态，仅重置超时计时器
                                            updateASRStatusHTML(`<span style="color:#06b6d4">✓ 继续对话中...</span>`);
                                            els.chatInput.value = '';
                                            const res = await handleMessageSend(result.text);
                                            if (res && res.code === 0) {
                                                console.log('发送成功:', res.data);
                                                // 发送成功后，清空输入框
                                                // els.chatInput.value = '';
                                            } else {
                                                console.log('发送失败:', res.message);
                                            }
                                        }
                                        // 重置唤醒状态计时器
                                        resetWakeWordTimeout();
                                    // 如果在唤醒状态，
                                    } else if (isWakeUp) {
                                        // 已唤醒状态下的普通对话，正常处理ASR结果
                                        els.chatInput.value = '';
                                        const res = await handleMessageSend(result.text);
                                        if (res && res.code === 0) {
                                            console.log('发送成功:', res.data);
                                            // 发送成功后，清空输入框
                                            // els.chatInput.value = '';
                                        } else {
                                            console.log('发送失败:', res.message);
                                        }
                                        
                                        // 重置唤醒状态计时器，延长唤醒状态
                                        resetWakeWordTimeout();
                                    } else {
                                        // 未唤醒状态下的普通对话，不做任何处理
                                        console.log('未处于唤醒状态，忽略语音输入');
                                        els.asrInfoElement.innerHTML = `<span style="color:#ff9f1c">⚠️ 请先说唤醒词来开始对话</span>`;
                                        showToast('请先说唤醒词来开始对话', 'error');
                                    }

                                    // 重置唤醒状态计时器的函数
                                    function resetWakeWordTimeout() {
                                        // 清除现有计时器
                                        if (wakeWordTimeoutId) {
                                            clearTimeout(wakeWordTimeoutId);
                                        }
                                        
                                        // 设置新计时器
                                        wakeWordTimeoutId = setTimeout(() => {
                                            // 唤醒状态超时
                                            isWakeUp = false;
                                            updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 唤醒状态已超时，请重新唤醒</span>`);
                                            showToast('唤醒状态已超时，请重新唤醒', 'error');
                                            updateWakeupStatusHTML(`<span style="color:#ff9f1c">数字人已休眠</span>`);
                                            console.log('唤醒状态已超时');
                                            
                                            closeFaceDetection();

                                            els.faceDetectionContainer.style.display = 'none';
                                        }, WAKE_WORD_TIMEOUT);
                                    }
                                }, (error) => {
                                    console.error('ASR结果处理失败:', error);
                                    els.asrInfoElement.innerHTML = '<span style="color:#f72585">❌ ASR结果处理失败: ' + error.message + '</span>';
                                });
                            }, ASR_SENTENCE_TIMEOUT);
                        }
                    }
                });
            }

            // 连接到ASR服务器
            // const Uri = document.getElementById('wssip').value;
            const Uri = "ws://127.0.0.1:10096/";
            await asrConnection.connect(Uri);
        } catch (error) {
            console.error('ASR连接失败:', error);
            updateASRStatusHTML('<span style="color: red;">ASR连接失败: ' + (error.message || '未知错误') + '</span>');
            throw error;
        }
    }

    function disconnectAllConnections() {
        dhConnection.disconnect();
        asrConnection.disconnect();
        ocConnection.disconnect();
    }

    /**
     * 获取当前选择的ASR模型模式
     * @returns {string} ASR模型模式：'2pass', 'online', 或 'offline'
     */
    function getAsrMode() {
        // 检查各个单选按钮的状态
        if (els.asrTwopassRadio && els.asrTwopassRadio.checked) {
            return '2pass';
        } else if (els.asrOnlineRadio && els.asrOnlineRadio.checked) {
            return 'online';
        } else if (els.asrOfflineRadio && els.asrOfflineRadio.checked) {
            return 'offline';
        }
        
        // 默认返回2pass模式
        return '2pass';
    }

    /**
     * 获取是否使用ITN（反向文本归一化）设置
     * @returns {boolean} 是否使用ITN
     */
    function getUseITN() {
        // 查找ITN相关的单选按钮
        const itnTrueRadio = document.getElementById('asr-itn-true');
        const itnFalseRadio = document.getElementById('asr-itn-false');
        
        // 检查是否选中了ITN
        if (itnTrueRadio && itnTrueRadio.checked) {
            return true;
        } else if (itnFalseRadio && itnFalseRadio.checked) {
            return false;
        }
        
        // 默认不使用ITN
        return false;
    }

    // 资源清理函数，在页面卸载前执行
    function cleanupDHConnectResources(options = {}) {
        console.log('执行资源清理...');
        const { saveSessionData = false } = options;

        // 保存会话数据（如果需要）
        if (saveSessionData) { 
            try {
                const sessionData = {
                    sessionId: document.getElementById('sessionid').value,
                    timestamp: new Date().toISOString(),
                    messages: []
                };

                // 获取聊天内容
                // if (els.chatContainer) {
                //     // 保存聊天记录
                //     const chatItems = els.chatContainer.querySelectorAll('.chat-item');
                //     chatItems.forEach(item => {
                //         const isSender = item.classList.contains('sender');
                //         const messageDiv = item.querySelector('div:not(img)');
                //         const message = messageDiv ? messageDiv.textContent : '';

                //         sessionData.messages.push({
                //             text: message,
                //             isSender
                //         });
                //     });
                // }

                // 将会话数据保存到localStorage
                localStorage.setItem('lastSessionData', JSON.stringify(sessionData));
                console.log('会话数据已保存到localStorage');
            } catch (e) {
                console.error('保存会话数据失败:', e);
            }
        }

        // 关闭所有连接
        disconnectAllConnections();

        // 关闭WebRTC连接
        webrtc.closeConnection(pc, els.videoElement, els.audioElement);

        console.log('资源清理完成');
        return true;
    }


    function clearInChatMessageInput() {
        els.chatInput.value = '';
    }

    function clearInSettingMessageInput() {
        els.messageInput.value = '';
    }

    function clearNbLivePath() {
        els.nbLivePathInput.value = '';
    }

    // 处理消息发送逻辑
    async function handleMessageSend(message, msgType = messageType, msgInterrupt = messageInterrupt) {
        console.log('正在处理消息发送...');
        
        // 从UI模块获取输入值
        // const message = els.messageInput.value;

        if (!message || !message.trim()) {
            return; // 不发送空消息
        }
        
        if (message) {
            const interrupt = msgInterrupt;
            const type = msgType;
            const sessionId = parseInt(els.sessionId.value);
            
            // 添加消息到聊天窗口
            
            // 检查是否是控制音乐的命令
            // const stopMusicResult = MusicPlayer.checkAndStopMusic(message, ['暂停音乐', '音乐暂停', '关闭音乐', '音乐关闭']);
            // const playMusicResult = MusicPlayer.checkAndPlayMusic(message, ['播放音乐', '音乐继续', '继续播放']);
            
            // 如果是音乐控制命令，则不发送到对话系统
            // if (stopMusicResult || playMusicResult) {
            //     console.log('检测到音乐控制命令，仅处理音乐控制，不发送到对话系统');
            //     return;
            // }
            
            // 发送消息到后端
            const res = await sendChatMessageToServer(message, type, sessionId, interrupt);
            if (res && res.code === 0) {
                showToast('发送成功!');
                if (type === 'echo') {
                    addUserMessage("复述: " + message);
                    addSystemMessage(message);
                } else {
                    addUserMessage(message);
                    addSystemMessage(res.data);
                }
            } else {
                showToast('发送消息失败: ' + res.data, 'error');
            }
            return res;

        }
    }

    async function setConfig() {
        const res = await setConfigToServer(parseInt(els.sessionId.value), personaText);
        console.log('提交人设配置后端响应:', res);
        if (res.code === 0) {
            showToast('提交成功!');
        } else {
            showToast('提交失败: ' + res.message, 'error');
        }
    }
    // 发送消息到后端
    async function sendChatMessageToServer(message, type, sessionId, interrupt) {
        const data = await request.httpPost('/human', {
            text: message,
            type: type,
            sessionid: sessionId,
            interrupt: interrupt
        });

        console.log('后端响应:', data);

        return data; // 返回响应数据，供调用者使用
    }

    async function getConfigFromServer(sessionId) {
        const data = await request.httpPost('/get_config', {
            sessionid: sessionId,
        });

        console.log('后端响应:', data);

        // 这里其实可以处理一下data.code

        // 清空输入框
        return data; // 返回响应数据，供调用者使用
    }

    async function setConfigToServer(sessionId, config) {
        const data = await request.httpPost('/set_config', {
            sessionid: sessionId,
            text: config
        });

        console.log('后端响应:', data);

        return data;
    }

    async function getDHPersonaText() {
        const res = await getConfigFromServer(parseInt(els.sessionId.value));
        if (res.code === 0) {
            personaText = res.data;
            els.configSelect.value = personaText;
        } else {
            showToast('获取人设配置失败: ' + res.message, 'error');
        }
    }

    async function submitNbLivePath() {
        if (!nbLivePath) {
            showToast('请输入文件夹路径!', 'error');
            return;
        }
        const res = await submitNbPathSubmitToServer(nbLivePath);
        if (res.code === 0) {
            showToast('提交成功!');
            clearNbLivePath();
        } else {
            showToast('提交失败: ' + res.message, 'error');
        }
    }

    async function submitNbPathSubmitToServer(path) {
        // const path = els.nbLivePath.value;
        const data = await request.httpPost('/nblivepath', {
            sessionid: parseInt(els.sessionId.value),
            path: path
        });

        console.log('后端响应:', data);

        return data;
    }

    // 背景模块
    /**
     * 设置背景媒体 - 支持视频、GIF和图片，简化版本
     * @param {string} type - 媒体类型: 'video', 'gif', 'image'
     * @param {string} src - 媒体源URL
     * @param {boolean} [loop=true] - 是否循环播放(仅视频)
     * @param {boolean} [muted=true] - 是否静音(仅视频)
     * @param {boolean} [autoplay=true] - 是否自动播放(仅视频)
     * @param {string} [position='cover'] - 背景定位方式: 'cover', 'contain', 'fill'
     * @param {boolean} [fadeIn=true] - 是否使用淡入效果
     * @param {number} [fadeTime=500] - 淡入时间(毫秒)
     * @param {Function} [callback=null] - 加载完成回调函数
     */
    function setBackgroundMedia(type, src, loop = true, muted = true, autoplay = true, position = 'fill', fadeIn = true, fadeTime = 500, callback = null) {
        // 移除所有活动类
        els.backgroundVideo.classList.remove('active');
        els.backgroundGif.classList.remove('active');
        els.backgroundImage.classList.remove('active');

        // 清空内容
        els.backgroundVideo.style.opacity = '';
        els.backgroundGif.style.opacity = '';
        els.backgroundImage.style.opacity = '';
        // 然后移除
        els.backgroundVideo.style.transition = '';
        els.backgroundGif.style.transition = '';
        els.backgroundImage.style.transition = '';
        
        // 清空内容
        els.backgroundVideo.innerHTML = '';
        els.backgroundGif.innerHTML = '';
        els.backgroundImage.innerHTML = '';
        
        // 确定目标元素
        let targetElement;
        
        switch (type.toLowerCase()) {
            case 'video':
                targetElement = els.backgroundVideo;
                break;
            case 'gif':
                targetElement = els.backgroundGif;
                break;
            case 'image':
                targetElement = els.backgroundImage;
                break;
            default:
                console.error('不支持的背景类型:', type);
                if (typeof callback === 'function') {
                    callback({
                        success: false,
                        error: `不支持的背景类型: ${type}`
                    });
                }
                return null;
        }
        
        // 创建媒体元素
        let mediaElement;
        
        switch (type.toLowerCase()) {
            case 'video':
                // 创建视频元素
                mediaElement = document.createElement('video');
                mediaElement.src = src;
                mediaElement.loop = loop;
                mediaElement.muted = muted;
                mediaElement.autoplay = autoplay;
                mediaElement.style.objectFit = position;
                mediaElement.style.width = '100%';
                mediaElement.style.height = '100%';
                
                // 设置加载事件
                mediaElement.addEventListener('loadeddata', function() {
                    finishLoading();
                });
                
                mediaElement.addEventListener('error', function(e) {
                    handleError(e);
                });
                break;
                
            case 'gif':
            case 'image':
                // 创建图片元素
                mediaElement = document.createElement('img');
                mediaElement.src = src;
                mediaElement.style.objectFit = position;
                mediaElement.style.width = '100%';
                mediaElement.style.height = '100%';
                
                // 设置加载事件
                mediaElement.onload = function() {
                    finishLoading();
                };
                
                mediaElement.onerror = function(e) {
                    handleError(e);
                };
                break;
        }
        
        // 添加元素到目标容器
        targetElement.appendChild(mediaElement);
        
        // 如果需要淡入效果，先设置为透明
        if (fadeIn) {
            // 然后延迟设置为透明
            targetElement.style.opacity = '0';
            targetElement.style.transition = `opacity ${fadeTime}ms ease`;
        }
        
        // 标记为活动状态
        targetElement.classList.add('active');
        
        // 处理加载完成
        function finishLoading() {
            if (fadeIn) {
                // 使用requestAnimationFrame确保浏览器有机会应用初始样式
                requestAnimationFrame(() => {
                    // 然后在下一帧应用新样式
                    requestAnimationFrame(() => {
                        targetElement.style.opacity = '1';
                    });
                });
            }
            
            // 初始化背景交互控制
            initBackgroundInteraction(targetElement, mediaElement);
            
            // 回调
            if (typeof callback === 'function') {
                callback({
                    success: true,
                    type: type.toLowerCase(),
                    element: mediaElement
                });
            }
        }
        
        // 处理错误
        function handleError(e) {
            console.error(`${type}背景加载失败:`, e);
            if (typeof callback === 'function') {
                callback({
                    success: false,
                    type: type.toLowerCase(),
                    error: e
                });
            }
        }
        
        // 返回媒体元素供外部使用
        return mediaElement;
    }

    /**
     * 清除所有背景
     * @param {boolean} [animate=true] - 是否使用淡出动画
     * @param {number} [fadeTime=500] - 淡出时间(毫秒)
     */
    function clearAllBackgrounds(animate = true, fadeTime = 500) {
        const elements = [els.backgroundVideo, els.backgroundGif, els.backgroundImage];
        
        els.forEach(el => {
            if (animate) {
                el.style.transition = `opacity ${fadeTime}ms ease`;
                el.style.opacity = '0';
                
                setTimeout(() => {
                    el.innerHTML = '';
                    el.classList.remove('active');
                    el.style.opacity = '';
                    el.style.transform = '';
                    el.style.transition = '';
                }, fadeTime);
            } else {
                el.style.opacity = '0';
                el.innerHTML = '';
                el.classList.remove('active');
                el.style.opacity = '';
                el.style.transform = '';
                el.style.transition = '';
            }
        });
    }

    /**
     * 初始化背景交互控制
     * @param {HTMLElement} container - 背景容器元素
     * @param {HTMLElement} mediaElement - 媒体元素(视频或图片)
     */
    function initBackgroundInteraction(container, mediaElement) {
        // 设置媒体元素的默认样式
        mediaElement.style.transformOrigin = 'center center';
        
        // 记录初始尺寸和位置，用于重置
        const initialState = {
            scale: 1,
            offsetX: 0,
            offsetY: 0
        };

        // mediaElement.style.position = "absolute";
        // mediaElement.style.zIndex = "1";
        // mediaElement.style.pointerEvents = "auto";
        
        // 给容器添加拖动功能，只在Alt键按下时激活
        // addDraggingEventHandle(
        //     mediaElement,             // 拖动元素
        //     mediaElement,             // 拖动把手(用容器本身)
        //     bgOffset,                     // offsetX
        //     false,                 // 不检查边界
        //     'draggable',        // 可拖动时添加的类
        //     'dragging',         // 拖动时添加的类
        //     () => isAltPressed,    // 只在Alt键按下时允许拖动
        //     null,
        //     els.bgXOffsetSlider,   // X轴滑块
        //     els.bgYOffsetSlider,   // Y轴滑块
        //     els.bgXOffsetValue,    // X轴输入框
        //     els.bgYOffsetValue,    // Y轴输入框
        //     true
        // );

        document.addEventListener('mousedown', function(e) {
            if (isAltPressed) {
                // 记录初始鼠标位置和元素位置
                const startX = e.clientX;
                const startY = e.clientY;
                const startMarginLeft = parseInt(mediaElement.style.marginLeft || '0');
                const startMarginTop = parseInt(mediaElement.style.marginTop || '0');

                // 获取元素和窗口尺寸
                const elementRect = mediaElement.getBoundingClientRect();
                const elementWidth = elementRect.width;
                const elementHeight = elementRect.height;
                const windowWidth = window.innerWidth;
                const windowHeight = window.innerHeight;
                
                // 定义吸附阈值（距离边缘多少像素时触发吸附）
                const snapThreshold = 10;
                
                const handleMouseMove = function(e) {
                    // 计算偏移量
                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;
                    
                    // 计算新位置
                    let newMarginLeft = startMarginLeft + deltaX;
                    let newMarginTop = startMarginTop + deltaY;
                    
                    // 计算元素四边的位置
                    const leftEdge = newMarginLeft;
                    const rightEdge = leftEdge + elementWidth;
                    const topEdge = newMarginTop;
                    const bottomEdge = topEdge + elementHeight;
                    
                    // 检查是否靠近窗口边缘并应用吸附效果
                    
                    // 左边缘吸附
                    if (Math.abs(leftEdge) < snapThreshold) {
                        newMarginLeft = 0;
                    }
                    
                    // 右边缘吸附
                    if (Math.abs(rightEdge - windowWidth) < snapThreshold) {
                        newMarginLeft = windowWidth - elementWidth;
                    }
                    
                    // 上边缘吸附
                    if (Math.abs(topEdge) < snapThreshold) {
                        newMarginTop = 0;
                    }
                    
                    // 下边缘吸附
                    if (Math.abs(bottomEdge - windowHeight) < snapThreshold) {
                        newMarginTop = windowHeight - elementHeight;
                    }
                    
                    // 水平居中吸附
                    const horizontalCenter = windowWidth / 2 - elementWidth / 2;
                    if (Math.abs(newMarginLeft - horizontalCenter) < snapThreshold) {
                        newMarginLeft = horizontalCenter;
                    }
                    
                    // 垂直居中吸附
                    const verticalCenter = windowHeight / 2 - elementHeight / 2;
                    if (Math.abs(newMarginTop - verticalCenter) < snapThreshold) {
                        newMarginTop = verticalCenter;
                    }
                    
                    // 应用新位置
                    mediaElement.style.marginLeft = `${newMarginLeft}px`;
                    mediaElement.style.marginTop = `${newMarginTop}px`;
                    
                    // 更新UI控件
                    updateSliderAndInput(els.bgXOffsetSlider, els.bgXOffsetValue, newMarginLeft);
                    updateSliderAndInput(els.bgYOffsetSlider, els.bgYOffsetValue, newMarginTop);
                };
                
                const handleMouseUp = function() {
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                };
                
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                e.preventDefault();
            }
        });
        
        // 添加滚轮事件用于缩放
        document.addEventListener('wheel', function(e) {
            // 只有在Alt键按下时才处理滚轮事件
            if (isAltPressed) {
                e.preventDefault();
                // 触发背景缩放事件
                console.log('触发背景缩放事件');
                
                // 获取当前缩放比例
                const currentScale = parseFloat(mediaElement.style.transform?.match(/scale\(([^)]+)\)/) ? 
                                    parseFloat(mediaElement.style.transform.match(/scale\(([^)]+)\)/)[1]) : 1);
                
                // 计算新的缩放比例
                let newScale;
                const scaleFactor = 0.1; // 缩放速度
                
                if (e.deltaY < 0) { // 放大
                    newScale = currentScale * (1 + scaleFactor);
                } else { // 缩小
                    newScale = currentScale * (1 - scaleFactor);
                }
                
                // 限制缩放范围
                newScale = Math.max(0.5, Math.min(3, newScale));
                
                // 应用新的缩放比例
                mediaElement.style.transform = `scale(${newScale})`;
                
                // 更新全局变量和UI控件
                if (els.bgScaleSlider && els.bgScaleValue) {
                    // 将比例转换为百分比显示
                    const scalePercent = Math.round(newScale * 100);
                    bgScale = updateSliderAndInput(els.bgScaleSlider, els.bgScaleValue, scalePercent);
                }
            }
        });
        
        // 添加键盘事件监听，用于微调位置
        // document.addEventListener('keydown', function(e) {
        //     // 只有在Alt键按下且背景容器获得焦点时处理方向键
        //     if (isAltPressed && document.activeElement === container) {
        //         const step = 10; // 每次移动的像素数
        //         let offsetX = parseInt(mediaElement.style.marginLeft || '0');
        //         let offsetY = parseInt(mediaElement.style.marginTop || '0');
                
        //         switch (e.key) {
        //             case 'ArrowLeft':
        //                 offsetX -= step;
        //                 break;
        //             case 'ArrowRight':
        //                 offsetX += step;
        //                 break;
        //             case 'ArrowUp':
        //                 offsetY -= step;
        //                 break;
        //             case 'ArrowDown':
        //                 offsetY += step;
        //                 break;
        //             case 'r': // 重置位置和缩放
        //                 offsetX = initialState.offsetX;
        //                 offsetY = initialState.offsetY;
        //                 mediaElement.style.transform = `scale(${initialState.scale})`;
        //                 if (els.bgScaleSlider && els.bgScaleValue) {
        //                     bgScale = updateSliderAndInput(els.bgScaleSlider, els.bgScaleValue, 100);
        //                 }
        //                 break;
        //             default:
        //                 return; // 不处理其他按键
        //         }
                
        //         // 应用新位置
        //         mediaElement.style.marginLeft = `${offsetX}px`;
        //         mediaElement.style.marginTop = `${offsetY}px`;
                
        //         // 更新UI控件
        //         bgOffset.x = updateSliderAndInput(els.bgXOffsetSlider, els.bgXOffsetValue, offsetX);
        //         bgOffset.y = updateSliderAndInput(els.bgYOffsetSlider, els.bgYOffsetValue, offsetY);
                
        //         e.preventDefault();
        //     }
        // });
        
        // 初始化UI控件状态
        bgScale = updateSliderAndInput(els.bgScaleSlider, els.bgScaleValue, 100);

        bgOffset.x = updateSliderAndInput(els.bgXOffsetSlider, els.bgXOffsetValue, 0);

        bgOffset.y = updateSliderAndInput(els.bgYOffsetSlider, els.bgYOffsetValue, 0);

        // 添加"宽度铺满"按钮事件
        els.bgFitWidth.addEventListener('click', function() {
            // 先将背景缩放比例重置同时缩放mediaElement 
            const fitType = 'cover';
            bgFit = fitType;
            bgFitHandle(mediaElement, fitType)
        });
        
        // 添加"高度铺满"按钮事件
        els.bgFitHeight.addEventListener('click', function() {
            // 先将背景缩放比例重置同时缩放mediaElement
            const fitType = 'contain';
            bgFit = fitType;
            bgFitHandle(mediaElement, fitType)
        });
        
        // 添加"高度铺满"按钮事件
        els.bgFitFill.addEventListener('click', function() {
            // 先将背景缩放比例重置同时缩放mediaElement
            const fitType = 'fill';
            bgFit = fitType;
            bgFitHandle(mediaElement, fitType)
        });
        
        // 将背景缩放滑块与媒体元素关联
        els.bgScaleSlider.addEventListener('input', function() {
            bgScale = parseInt(this.value);
            mediaElement.style.transform = `scale(${bgScale / 100})`;
            
            // 更新输入框
            bgScale = updateSliderAndInput(els.bgScaleSlider, els.bgScaleValue, this.value);
        });
        
        // 将背景X偏移滑块与媒体元素关联
        els.bgXOffsetSlider.addEventListener('input', function() {
            mediaElement.style.marginLeft = `${this.value}px`;
            
            // 更新输入框
            bgOffset.x = updateSliderAndInput(els.bgXOffsetSlider, els.bgXOffsetValue, this.value);
        });
        
        // 将背景Y偏移滑块与媒体元素关联
        els.bgYOffsetSlider.addEventListener('input', function() {
            mediaElement.style.marginTop = `${this.value}px`;
            
            // 更新输入框
            bgOffset.y = updateSliderAndInput(els.bgYOffsetSlider, els.bgYOffsetValue, this.value);
        });
        

    }

    /**
     * 背景文件上传处理
     * @param {Event} event - 文件选择事件
     */
    function handleBackgroundUpload(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const fileType = file.type;
        const url = URL.createObjectURL(file);
        
        if (fileType.startsWith('video/')) {
            // 在回调中释放URL
            setBackgroundMedia('video', url, true, true, true, 'cover', true, 500, function(result) {
                if (result.success) {
                    // 媒体已加载，安全释放URL
                    URL.revokeObjectURL(url);
                }
            });
        } else if (fileType === 'image/gif') {
            setBackgroundMedia('gif', url, true, true, true, 'cover', true, 500, function(result) {
                if (result.success) {
                    URL.revokeObjectURL(url);
                }
            });
        } else if (fileType.startsWith('image/')) {
            setBackgroundMedia('image', url, true, true, true, 'cover', true, 500, function(result) {
                if (result.success) {
                    URL.revokeObjectURL(url);
                }
            });
        } else {
            alert('不支持的文件类型。请上传图片或视频文件。');
            URL.revokeObjectURL(url);
        }
        
        // 重置文件输入，确保下次选择同一文件时触发change事件
        event.target.value = '';
    }

    // 消息框添加逻辑
    /**
     * 添加消息到聊天框
     * @param {HTMLElement} container 消息容器
     * @param {string} content 消息内容
     * @param {boolean} isSystem 是否为系统消息
     * @param {string} position 消息位置 ('left' 或 'right')
     * @param {boolean} isStream 是否为流式消息
     * @returns {HTMLElement} 消息容器
     */
    function addChatMessage(container, content, isSystem = true, position = 'right', isStream = false) {
        // 如果是流式消息
        if (isStream) {
            // 如果已有当前流式消息容器且未超时，则追加内容
            if (currentStreamContainer && !isStreamTimeout()) {
                appendToMessage(currentStreamContainer, content);
                return currentStreamContainer;
            }
            // 如果没有容器或已超时，创建新的流式消息
            currentStreamContainer = createNewChatMessage(content, isSystem, position, true);
            return currentStreamContainer;
        }

        // 如果是用户消息，重置流式消息状态
        if (!isSystem) {
            resetStreamState();
        }
        
        // 创建新的普通消息
        return createNewChatMessage(content, isSystem, position, false);
    }

    /**
     * 检查流式消息是否超时
     */
    function isStreamTimeout() {
        if (!currentStreamId) return false;
        const now = Date.now();
        return (now - lastUserMessageTime) > STREAM_TIMEOUT;
    }

    /**
     * 创建新的聊天消息
     */
    function createNewChatMessage(content, isSystem, position, isStream) {
        // 创建外层容器
        const chatItem = document.createElement('div');
        chatItem.className = `chat-item${!isSystem ? ' user' : ''}`;

        // 创建头像
        const avatar = document.createElement('img');
        avatar.src = isSystem ? SYSTEM_AVATAR : USER_AVATAR;
        avatar.alt = isSystem ? 'AI' : 'User';
        avatar.className = 'avatar';
        
        // 创建消息容器
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message';
        if (isStream) {
            messageDiv.classList.add('stream');
            currentStreamId = Date.now().toString();
        }
        messageDiv.innerHTML = content;

        // 为消息中的所有图片添加点击事件（排除头像）
        const images = messageDiv.getElementsByTagName('img');
        Array.from(images).forEach(image => {
            // 确保不是头像
            if (!image.classList.contains('avatar')) {
                image.style.cursor = 'pointer';
                image.addEventListener('click', (event) => {
                    const imageSrc = event.target.src;
                    els.imagePreview.src = imageSrc;
                    els.imagePreviewModal.style.display = 'flex';
                });
            }
        });

        // 组装消息
        chatItem.appendChild(avatar);
        chatItem.appendChild(messageDiv);
        
        // 插入到聊天内容区
        if (position === 'right') {
            els.chatContainer.appendChild(chatItem);
        } else {
            els.chatContainer.insertBefore(chatItem, els.chatContainer.firstChild);
        }
        
        // 更新最后消息内容
        lastMessageContent = content;
        
        // 滚动到最新消息
        scrollToLatestMessage();
        
        return messageDiv;
    }

    /**
     * 添加内容到现有消息
     */
    function appendToMessage(container, content) {
        if (!container) return;
        container.innerHTML += content;
        scrollToLatestMessage();
    }

    /**
     * 添加系统消息的快捷方法
     */
    function addSystemMessage(content, position = 'right') {
        // 系统消息不重置流式状态
        return addChatMessage(null, content, true, position, false);
    }

    /**
     * 添加用户消息的快捷方法
     */
    function addUserMessage(content, position = 'right') {
        lastUserMessageTime = Date.now();
        // 用户消息会重置流式状态
        resetStreamState();
        return addChatMessage(null, content, false, position, false);
    }

    /**
     * 添加流式消息的快捷方法
     */
    function addStreamMessage(content, container = null, position = 'right') {
        return addChatMessage(container, content, true, position, true);
    }

    /**
     * 处理系统流式消息
     */
    function handleSystemMessageStream(content) {
        if (!currentStreamId) {
            // 创建新的流式消息
            currentStreamContainer = addSystemMessage(content);
            currentStreamId = Date.now().toString();
        } else {
            // 追加到当前流式消息
            if (currentStreamContainer) {
                appendToMessage(currentStreamContainer, content);
            } else {
                // 如果丢失了容器引用，创建新消息
                addSystemMessage(content);
            }
        }
        return currentStreamContainer;
    }

    /**
     * 重置流式消息状态
     */
    function resetStreamState() {
        currentStreamId = null;
        currentStreamContainer = null;
    }

    /**
     * 滚动到最新消息
     */
    function scrollToLatestMessage() {
        els.chatContainer.scrollTop = els.chatContainer.scrollHeight;
    }

    //#region UI 工具方法

    /**
     * 添加拖动事件处理
     * @param {HTMLElement} element - 需要拖动的DOM元素主体
     * @param {HTMLElement} dragHandle - 触发拖动的把手元素(如果为null，则使用element本身)
     * @param {Object|number} offsets - 偏移量对象，包含x和y属性
     * @param {boolean} checkBoundary - 是否检查边界
     * @param {string} draggableClass - 可拖动时添加到把手的CSS类
     * @param {string} draggingClass - 拖动时添加到把手的CSS类
     * @param {Function} isDraggableFn - 判断是否可拖动的函数
     * @param {HTMLElement} changeOffsetElement.xOffsetSlider - X轴偏移滑块元素
     * @param {HTMLElement} changeOffsetElement.yOffsetSlider - Y轴偏移滑块元素
     * @param {HTMLElement} changeOffsetElement.xOffsetInput - X轴偏移输入框元素
     * @param {HTMLElement} changeOffsetElement.yOffsetInput - Y轴偏移输入框元素
     * @returns {Function} 清理函数，用于移除事件监听器
     */
    function addDraggingEventHandle(element, dragHandle, offsets, checkBoundary = true, draggableClass = 'draggable', draggingClass = 'dragging', isDraggableFn = () => true, changeOffsetElement) {
        // 检查输入参数
        if (!element) {
            console.error("拖动处理函数缺少必要参数");
            return false;
        }
        
        // 如果没有提供拖动把手，则使用元素本身
        dragHandle = dragHandle || element;
        
        // 确保offsets是一个有效的对象
        if (!offsets || typeof offsets !== 'object') {
            offsets = { x: 0, y: 0 };
        }
        
        // 为每个拖动实例分配唯一ID
        const dragInstanceId = Date.now() + Math.random().toString(36).substr(2, 5);
        
        // 拖动状态标记
        let dragRequestId = null;
        let originalTransition = '';
        
        // 初始应用样式
        if (draggableClass && typeof isDraggableFn === 'function' && isDraggableFn()) {
            dragHandle.classList.add(draggableClass);
        }
        
        // 重置拖动状态的辅助函数
        const resetDraggingState = function() {
            // 只有当当前拖动实例是活动的才重置
            if (window.activeDragInstanceId === dragInstanceId) {
                window.isDragging = false;
                window.activeDragInstanceId = null;
            
            // 恢复把手样式
            if (draggingClass) {
                dragHandle.classList.remove(draggingClass);
            }
            
            if (draggableClass) {
                dragHandle.classList.add(draggableClass);
            }
                
                // 恢复原始 transition
                element.style.transition = originalTransition;
            
            if (dragRequestId) {
                cancelAnimationFrame(dragRequestId);
                dragRequestId = null;
                }
            }
        };
        
        // 鼠标按下事件处理
        const handleMouseDown = function(e) {
            if (typeof isDraggableFn === 'function' && !isDraggableFn()) return;
            
            // 设置全局拖动状态和当前活动拖动实例
            window.isDragging = true;
            window.activeDragInstanceId = dragInstanceId;
            
            // 保存并禁用 transition
            originalTransition = element.style.transition;
            element.style.transition = 'none';
            
            // 添加拖动状态类到把手
            if (draggingClass) {
                if (draggableClass) dragHandle.classList.remove(draggableClass);
                dragHandle.classList.add(draggingClass);
            }
            
            // 获取元素当前位置
            const elementRect = element.getBoundingClientRect();
            
            // 计算鼠标点击位置与元素当前位置的差值
            offsets.x = e.clientX - elementRect.left;
            offsets.y = e.clientY - elementRect.top;
            
            // 防止拖动时选择其他元素
            e.preventDefault();
        };
        
        const handleMouseMove = function(e) {
            if (window.isDragging && window.activeDragInstanceId === dragInstanceId) {
                if (!dragRequestId) {
                    dragRequestId = requestAnimationFrame(() => {
                        // 直接计算新位置，不需要额外的偏移计算
                        let left = e.clientX - offsets.x;
                        let top = e.clientY - offsets.y;
                        
                        // 如果需要边界检查
                        if (checkBoundary) {
                            const windowWidth = window.innerWidth;
                            const windowHeight = window.innerHeight;
                            const elementRect = element.getBoundingClientRect();
                            
                            // 边界检查
                            left = Math.max(0, Math.min(left, windowWidth - elementRect.width));
                            top = Math.max(0, Math.min(top, windowHeight - elementRect.height));
                        }
                        
                        // 设置新位置
                        element.style.left = `${left}px`; 
                        element.style.top = `${top}px`;
                        
                        // 更新滑块元素值和输入框值
                        if (changeOffsetElement) {
                            updateSliderAndInput(changeOffsetElement.xOffsetSlider, changeOffsetElement.xOffsetInput, left);
                            updateSliderAndInput(changeOffsetElement.yOffsetSlider, changeOffsetElement.yOffsetInput, top);
                        }
                        
                        dragRequestId = null;
                    });
                }
            }
        };
        
        // 鼠标释放事件处理
        const handleMouseUp = function() {
            if (window.isDragging && window.activeDragInstanceId === dragInstanceId) {
                resetDraggingState();
            }
        };
        
        // 触摸事件处理函数保持类似逻辑，不再重复
        const handleTouchStart = function(e) {
            // if (typeof isDraggableFn === 'function' && !isDraggableFn()) return; // 阻止拖动
            if (e.touches.length === 1) {
                // 设置全局拖动状态和当前活动拖动实例
                window.isDragging = true;
                window.activeDragInstanceId = dragInstanceId;
                
                originalTransition = element.style.transition;
                element.style.transition = 'none';

                // 添加拖动状态类到把手
                if (draggingClass) {
                    if (draggableClass) dragHandle.classList.remove(draggableClass);
                    dragHandle.classList.add(draggingClass);
                }
                
                // 计算触摸点与拖动把手左上角的偏移
                const touch = e.touches[0];
                const rect = dragHandle.getBoundingClientRect();
                
                // 对于使用left/top的元素，直接计算点击位置相对于元素左上角的偏移
                offsets.x = touch.clientX - rect.left;
                offsets.y = touch.clientY - rect.top;
                
                // 防止触摸时的页面滚动
                // e.preventDefault();
            }
        };
        
        const handleTouchMove = function(e) {
            // 只有当前拖动实例是活动的才处理移动
            if (window.isDragging && window.activeDragInstanceId === dragInstanceId && e.touches.length === 1) {
                const touch = e.touches[0];
                
                if (!dragRequestId) {
                    dragRequestId = requestAnimationFrame(() => {
                        // 获取窗口尺寸
                        const windowWidth = window.innerWidth;
                        const windowHeight = window.innerHeight;
                        
                        // 获取元素主体尺寸（用于边界检查）
                        const elementRect = element.getBoundingClientRect();
                        const elementWidth = elementRect.width;
                        const elementHeight = elementRect.height;
                        
                        // 获取拖动把手相对于元素主体的位置差异
                        const handleRect = dragHandle.getBoundingClientRect();
                        const handleOffsetLeft = handleRect.left - elementRect.left;
                        const handleOffsetTop = handleRect.top - elementRect.top;
                        
                        // 计算新位置（考虑把手和主体的相对位置）
                        let left = touch.clientX - offsets.x - handleOffsetLeft;
                        let top = touch.clientY - offsets.y - handleOffsetTop;
                        
                        // 如果需要边界检查（针对整个元素主体）
                        if (checkBoundary) {
                            // 左边界检查
                            left = Math.max(left, 0);
                            // 右边界检查 (确保元素完全在屏幕内)
                            left = Math.min(left, windowWidth - elementWidth);
                            // 上边界检查
                            top = Math.max(top, 0);
                            // 下边界检查 (确保元素完全在屏幕内)
                            top = Math.min(top, windowHeight - elementHeight);
                        }
                        
                        // 使用left/top来定位
                        element.style.left = `${left}px`;
                        element.style.top = `${top}px`;
                        
                        // 更新滑块元素值和输入框值
                        if (changeOffsetElement) {
                            updateSliderAndInput(changeOffsetElement.xOffsetSlider, changeOffsetElement.xOffsetInput, left);
                            updateSliderAndInput(changeOffsetElement.yOffsetSlider, changeOffsetElement.yOffsetInput, top);
                        }
                        
                        dragRequestId = null;
                    });
                }
                
                // 阻止页面滚动等默认行为
                // e.preventDefault();
            }
        };
        
        const handleTouchEnd = handleMouseUp;
        
        // 绑定事件
        dragHandle.addEventListener("mousedown", handleMouseDown);
        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
        dragHandle.addEventListener("touchstart", handleTouchStart);
        document.addEventListener("touchmove", handleTouchMove);
        document.addEventListener("touchend", handleTouchEnd);
        
        // 返回清理函数
        return function cleanup() {
            // 移除样式
            if (draggableClass) dragHandle.classList.remove(draggableClass);
            if (draggingClass) dragHandle.classList.remove(draggingClass);
            
            // 移除事件监听器
            dragHandle.removeEventListener("mousedown", handleMouseDown);
            document.removeEventListener("mousemove", handleMouseMove);
            document.removeEventListener("mouseup", handleMouseUp);
            dragHandle.removeEventListener("touchstart", handleTouchStart);
            document.removeEventListener("touchmove", handleTouchMove);
            document.removeEventListener("touchend", handleTouchEnd);
        };
    }

    // 添加拖动事件处理
    /**
     * 为指定元素添加拖动和（可选）缩放功能。
     *
     * @param {HTMLElement} element - 需要添加拖动事件的 HTML 元素。
     * @param {{ x: number, y: number }} offsets - 拖动起始的偏移量，包含 x 和 y 坐标。
     * @param {{ width: number, height: number }} size - 元素的初始尺寸（宽度和高度），用于缩放计算。
     * @param {boolean} isResize - 是否启用缩放功能。如果为 true，将允许调整元素大小。
     * @param {HTMLElement} [changeOffsetElement] - 需要同步更新偏移量样式的元素（如外层容器），可选参数。
     *
     * @example
     * const el = document.getElementById('draggable');
     * addResizeEventHandle(el, { x: 0, y: 0 }, { width: '100%', height: '100vh' }, true, document.getElementById('container'));
     */
    function addResizeEventHandle(element, offsets, size, checkBoundary = true, isResize = () => true, changeOffsetElement, handleSize = 20, disableElementsWhileResizing = []) {
        const isResizeFn = typeof isResize === 'function'? isResize : () => isResize;

        if (!element ||!offsets ||!size) {
            console.error("缺少必要参数");
            return false;
        }

        // 定义8个方向的拖动手柄
        const directions = [
            'n', 's', 'e', 'w',  // 上下左右
            'nw', 'ne', 'sw', 'se'  // 四个角
        ];

        // 创建拖动手柄的样式
        const handleStyle = {
            position: 'absolute',
            width: `${handleSize}px`,
            height: `${handleSize}px`,
            backgroundColor: 'transparent',
            zIndex: 1000
        };

        // 定义每个方向手柄的特定样式
        const directionStyles = {
            n: {
                top: `-${handleSize / 2}px`,
                left: '50%',
                transform: 'translateX(-50%)',
                cursor: 'n-resize',
                height: `${handleSize}px`,
                width: '100%'
            },
            s: {
                bottom: `-${handleSize / 2}px`,
                left: '50%',
                transform: 'translateX(-50%)',
                cursor: 's-resize',
                height: `${handleSize}px`,
                width: '100%'
            },
            e: {
                right: `-${handleSize / 2}px`,
                top: '50%',
                transform: 'translateY(-50%)',
                cursor: 'e-resize',
                width: `${handleSize}px`,
                height: '100%'
            },
            w: {
                left: `-${handleSize / 2}px`,
                top: '50%',
                transform: 'translateY(-50%)',
                cursor: 'w-resize',
                width: `${handleSize}px`,
                height: '100%'
            },
            nw: {
                top: `-${handleSize / 2}px`,
                left: `-${handleSize / 2}px`,
                cursor: 'nw-resize',
                width: `${handleSize}px`,
                height: `${handleSize}px`
            },
            ne: {
                top: `-${handleSize / 2}px`,
                right: `-${handleSize / 2}px`,
                cursor: 'ne-resize',
                width: `${handleSize}px`,
                height: `${handleSize}px`
            },
            sw: {
                bottom: `-${handleSize / 2}px`,
                left: `-${handleSize / 2}px`,
                cursor: 'sw-resize',
                width: `${handleSize}px`,
                height: `${handleSize}px`
            },
            se: {
                bottom: `-${handleSize / 2}px`,
                right: `-${handleSize / 2}px`,
                cursor: 'se-resize',
                width: `${handleSize}px`,
                height: `${handleSize}px`
            }
        };

        // 创建并添加拖动手柄
        const handles = {};
        directions.forEach(dir => {
            const handle = document.createElement('div');
            Object.assign(handle.style, handleStyle, directionStyles[dir]);
            handle.className = `resize-handle resize-handle-${dir}`;
            element.appendChild(handle);
            handles[dir] = handle;
        });

        // 最小尺寸限制
        const minWidth = 305;
        const minHeight = 240;

        // 记录初始尺寸的单位
        let initialWidthUnit = '';
        let initialHeightUnit = '';
        // 拖动状态
        let isDragging = false;
        let currentHandle = null;
        let startX = 0;
        let startY = 0;
        let startWidth = 0;
        let startHeight = 0;
        let startLeft = 0;
        let startTop = 0;
        let originalTransition = '';

        // 获取尺寸单位的辅助函数
        function getUnit(value) {
            // 如果是数字就转为字符串
            value = value + '';
            const match = value.match(/[a-z%]+$/);
            return match? match[0] : 'px';
        }

        // 修改禁用/启用事件的辅助函数
        function disableElements() {
            if (!Array.isArray(disableElementsWhileResizing)) {
                return;
            }

            disableElementsWhileResizing.forEach(el => {
                if (el && el instanceof HTMLElement) {
                    // 保存原始样式
                    el._originalStyles = {
                        pointerEvents: el.style.pointerEvents,
                        userSelect: el.style.userSelect,
                        cursor: el.style.cursor
                    };

                    // 应用禁用样式
                    el.style.pointerEvents = 'none';
                    el.style.userSelect = 'none';
                    el.style.cursor = 'not-allowed';
                }
            });
        }

        function enableElements() {
            if (!Array.isArray(disableElementsWhileResizing)) {
                return;
            }

            disableElementsWhileResizing.forEach(el => {
                if (el && el instanceof HTMLElement && el._originalStyles) {
                    // 恢复原始样式
                    el.style.pointerEvents = el._originalStyles.pointerEvents;
                    el.style.userSelect = el._originalStyles.userSelect;
                    el.style.cursor = el._originalStyles.cursor;

                    // 清除临时存储的数据
                    delete el._originalStyles;
                }
            });
        }


        // 处理鼠标按下事件
        function handleMouseDown(e, direction) {
            if (!isResizeFn()) return;

            e.preventDefault();
            e.stopPropagation();

            isDragging = true;
            currentHandle = direction;

            // 保存并禁用 transition
            originalTransition = element.style.transition;
            element.style.transition = 'none';

            // 禁用指定元素的事件
            disableElements();

            // 记录初始状态及尺寸单位
            startX = e.clientX;
            startY = e.clientY;
            const rect = element.getBoundingClientRect();
            startWidth = parseFloat(rect.width);
            startHeight = parseFloat(rect.height);
            initialWidthUnit = getUnit(rect);
            initialHeightUnit = getUnit(rect.height);
            startLeft = rect.left;
            startTop = rect.top;

            // 添加临时事件监听器
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        }

        // 处理鼠标移动事件
        function handleMouseMove(e) {
            if (!isDragging) return;

            e.preventDefault();
            // 在请求动画帧之前保存当前手柄的方向
            const handleDirection = currentHandle;
            if (!handleDirection) return;

            requestAnimationFrame(() => {
                if (!isDragging) return;

                const dx = e.clientX - startX;
                const dy = e.clientY - startY;

                let newWidth = startWidth;
                let newHeight = startHeight;
                let newLeft = startLeft;
                let newTop = startTop;

                const boundary = element.parentElement || document.documentElement;
                const boundaryRect = boundary.getBoundingClientRect();

                // 计算缩放后的位置和大小
                if (handleDirection.includes('e')) {
                    newWidth = Math.max(minWidth, startWidth + dx);
                }
                if (handleDirection.includes('w')) {
                    newWidth = Math.max(minWidth, startWidth - dx);
                    newLeft = startLeft + (startWidth - newWidth);
                }
                if (handleDirection.includes('s')) {
                    newHeight = Math.max(minHeight, startHeight + dy);
                }
                if (handleDirection.includes('n')) {
                    newHeight = Math.max(minHeight, startHeight - dy);
                    newTop = startTop + (startHeight - newHeight);
                }

                // 👉 边界限制逻辑
                if (checkBoundary) {
                    const maxLeft = boundaryRect.width - minWidth;
                    const maxTop = boundaryRect.height - minHeight;

                    // 限制 left 和 top 不小于 0，不大于边界
                    newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                    newTop = Math.max(0, Math.min(newTop, maxTop));

                    // 限制宽度和高度不超出边界
                    if (newLeft + newWidth > boundaryRect.width) {
                        newWidth = boundaryRect.width - newLeft;
                    }
                    if (newTop + newHeight > boundaryRect.height) {
                        newHeight = boundaryRect.height - newTop;
                    }

                    // 最小值保障
                    newWidth = Math.max(minWidth, newWidth);
                    newHeight = Math.max(minHeight, newHeight);
                }

                // 根据保存的单位来更新样式
                element.style.width = `${newWidth}${initialWidthUnit}`;
                element.style.height = `${newHeight}${initialHeightUnit}`;
                element.style.left = `${newLeft}px`;
                element.style.top = `${newTop}px`;

                // 更新偏移量和尺寸
                if (offsets) {
                    offsets.x = newLeft;
                    offsets.y = newTop;
                }
                if (size) {
                    size.width = newWidth;
                    size.height = newHeight;
                }

                // 更新控制元素
                if (changeOffsetElement) {
                    if (changeOffsetElement.xOffsetSlider && changeOffsetElement.xOffsetInput) {
                        updateSliderAndInput(changeOffsetElement.xOffsetSlider, changeOffsetElement.xOffsetInput, newLeft);
                    }
                    if (changeOffsetElement.yOffsetSlider && changeOffsetElement.yOffsetInput) {
                        updateSliderAndInput(changeOffsetElement.yOffsetSlider, changeOffsetElement.yOffsetInput, newTop);
                    }
                    if (changeOffsetElement.widthSlider && changeOffsetElement.widthInput) {
                        updateSliderAndInput(changeOffsetElement.widthSlider, changeOffsetElement.widthInput, newWidth);
                    }
                    if (changeOffsetElement.heightSlider && changeOffsetElement.heightInput) {
                        updateSliderAndInput(changeOffsetElement.heightSlider, changeOffsetElement.heightInput, newHeight);
                    }
                }
            });

        }

        // 处理鼠标松开事件
        function handleMouseUp() {
            if (isDragging) {
                // 恢复原始 transition
                element.style.transition = originalTransition;

                // 重新启用指定元素的事件
                enableElements();

                isDragging = false;
                currentHandle = null;
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            }
        }

        // 为每个手柄添加事件监听器
        Object.entries(handles).forEach(([direction, handle]) => {
            handle.addEventListener('mousedown', (e) => handleMouseDown(e, direction));
        });

        // 返回清理函数
        return function cleanup() {
            // 移除所有手柄
            Object.values(handles).forEach(handle => {
                handle.removeEventListener('mousedown', handleMouseDown);
                element.removeChild(handle);
            });
        };
    }

    function eventDHWheelHandle(e, element, scaleFactorSpeed = 0.1, isDraggable) {
        // 检查输入参数
        if (!element) {
            console.error("处理函数缺少必要参数");
            return false;
        }
        
        // 缩放比例/速度
        const scaleFactor = scaleFactorSpeed;

        // 如果正在拖动中，不执行缩放操作，防止位置计算错误
        if (window.isDragging) {
            return false;
        }

        if (isDraggable) {
            // 获取当前画布位置和尺寸
            let currentWidth = parseInt(element.style.width) || initialWidth;
            let currentHeight = parseInt(element.style.height) || initialHeight;
            
            // 计算新尺寸
            let newWidth, newHeight;
            if (e.deltaY < 0) { // 放大 - 滚轮向上或触摸扩大
                newWidth = Math.round(currentWidth * (1 + scaleFactor));
            } else { // 缩小 - 滚轮向下或触摸收缩
                newWidth = Math.round(currentWidth * (1 - scaleFactor));
            }
            
            // 计算高度 - 根据比例锁定状态
            if (isRatioLocked && aspectRatio) {
                // 使用原始比例
                newHeight = Math.round(newWidth / aspectRatio);
            } else if (customRatioLocked && customAspectRatio) {
                // 使用自定义比例
                newHeight = Math.round(newWidth / customAspectRatio);
            } else {
                // 未锁定比例但仍保持当前比例进行缩放
                const currentRatio = currentHeight > 0 ? currentWidth / currentHeight : (aspectRatio || 16/9);
                newHeight = Math.round(newWidth / (isFinite(currentRatio) ? currentRatio : (aspectRatio || 16/9)));
            }
            
            // 使用setupCanvasSize方法来设置新尺寸，并保持中心点不变
            setupCanvasSize(
                newWidth,            // 新宽度
                newHeight,           // 新高度
                true,               // 强制更新
                true,               // 限制最大尺寸
                maxCanvasResolution, // 最大高度限制
                'center',           // 锚点: 中心点
                null                // 不使用额外动画
            );
        }
    }

    function bgFitHandle(mediaElement, fitType = 'cover') {
        // 先将背景缩放比例重置同时缩放mediaElement 
        bgScale = updateSliderAndInput(els.bgScaleSlider, els.bgScaleValue, 100);
        bgOffset.x = updateSliderAndInput(els.bgXOffsetSlider, els.bgXOffsetValue, 0);
        bgOffset.y = updateSliderAndInput(els.bgYOffsetSlider, els.bgYOffsetValue, 0);
        mediaElement.style.marginLeft = bgOffset.x;
        mediaElement.style.marginTop = bgOffset.y;
        mediaElement.style.transform = `scale(${bgScale / 100})`;
        mediaElement.style.objectFit = fitType;
    }

    // 滑块及其输入数据更新逻辑通用处理
    function updateSliderAndInput(slider, input, value) {
        if (value !== null) {
            if (slider) {
                slider.value = value;
            }
            if (input) {
                input.value = value;
            }
        }

        return value;
    }

    // 隐藏UI元素的函数
    function hideUIElements() {
        if (useBehindUi) {
            document.querySelectorAll('[data-ui="ui"]').forEach(element => {
                if (element) {
                    element.classList.add('ui-hidden');
                }
            });
        }
    }
    // 显示UI元素的函数
    function showUIElements() {
        // els.uiElements.forEach(element => {
        //     if (element) {
        //         element.classList.remove('ui-hidden');
        //     }
        // });
        
        

        // 添加空值检查
        if (!els.uiElements) {
            console.warn('uiElements 未初始化，尝试重新获取');
            // 重新获取 UI 元素
            updateElement('uiElements', document.querySelectorAll('[data-ui]'));
        }
        
        // 确保 els.uiElements 存在且是可迭代的
        if (els.uiElements && typeof els.uiElements.forEach === 'function') {
            els.uiElements.forEach(element => {
                if (element) {
                    element.classList.remove('ui-hidden');
                }
            });
        } else {
            console.warn('uiElements 不是有效的 NodeList 或数组');
            return;
        }


        // 重置计时器
        if (uiHideTimer) {
            clearTimeout(uiHideTimer);
            uiHideTimer = null;
        }
        
        // 如果启用了自动隐藏，设置新的计时器
        if (useBehindUi) {
            uiHideTimer = setTimeout(hideUIElements, useBehindUiTime);
        }
    }
    // 显示提示弹窗
    function showToast(message, type = 'success', duration = 3000) {
        console.log('进入显示框');
        els.toastMessage.textContent = message;
        
        // 根据类型设置不同的背景色
        if (type === 'error') {
            els.successToast.style.backgroundColor = '#f44336';
        } else if (type === 'info') {
            els.successToast.style.backgroundColor = '#2196F3';
        } else if (type === 'warning') {
            els.successToast.style.backgroundColor = '#ff9800';
        } else {
            els.successToast.style.backgroundColor = '#4caf50';
        }
        
        // 显示弹窗
        els.successToast.style.display = 'block';
        
        // 动画显示
        setTimeout(() => {
            els.successToast.style.opacity = 1;
        }, 10);
        
        // 指定时间后消失
        setTimeout(() => {
            els.successToast.style.opacity = 0;
            
            // 等待淡出动画完成后隐藏
            setTimeout(() => {
                els.successToast.style.display = 'none';
            }, 300);
        }, duration);
    }

    // 更新OC状态HTML
    function updateOCStatusHTML(html) {
        els.statusElement.innerHTML = html;
    }

    // 更新ASR状态HTML
    function updateASRStatusHTML(html) {
        els.asrInfoElement.innerHTML = html;
    }

    // 更新唤醒词状态HTML
    function updateWakeupStatusHTML(html) {
        els.wakeupInfoElement.innerHTML = html;
    }

    // 应用主题，待整理
    // 注意这里直接用onclick事件绑定，而不是addEventListener
    function applyTheme(theme) {
        const opacity = els.modalOpacitySlider.value;
        let systemBg, systemColor, userBg, userColor;
        
        let aiAvatarSvg;
        let aiAvatars;

        switch(theme) {
            case 'light':
                els.chatModal.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;
                els.chatModalheader.style.backgroundColor = `rgba(74, 144, 226, ${opacity})`;
                els.chatModalheader.style.color = '#fff';
                els.chatContainer.style.color = '#333';
                els.chatInputArea.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;
                els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(74, 144, 226)`;
                // 按钮文本颜色白色
                els.sendMessageButtonInChatModal.style.color = '#fff';
                systemBg = 'rgba(240, 240, 240, 0.9)';
                systemColor = '#333';
                userBg = 'rgba(74, 144, 226, 0.9)';
                userColor = '#fff';
                // Save original RGB values
                originalModalRGB = '255, 255, 255';
                originalHeaderRGB = '74, 144, 226';
                originalContainerRGB = '51, 51, 51';
                originalSystemMessageRGB = '240, 240, 240';
                originalUserMessageRGB = '74, 144, 226';
                originalInputAreaRGB = '255, 255, 255';
                originalChatSendButtonRGB = '74, 144, 226';
                break;
            case 'dark':
                els.chatModal.style.backgroundColor = `rgba(33, 37, 41, ${opacity})`;
                els.chatModalheader.style.backgroundColor = `rgba(13, 17, 21, ${opacity})`;
                els.chatModalheader.style.color = '#fff';
                els.chatContainer.style.color = '#fff';
                els.chatInputArea.style.backgroundColor = `rgba(13, 17, 21, ${opacity})`;
                els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(33, 37, 41)`;
                // 按钮文本颜色
                els.sendMessageButtonInChatModal.style.color = '#fff';
                systemBg = 'rgba(52, 58, 64, 0.9)';
                systemColor = '#fff';
                userBg = 'rgba(74, 144, 226, 0.9)';
                userColor = '#fff';
                // Save original RGB values
                originalModalRGB = '33, 37, 41';
                originalHeaderRGB = '13, 17, 21';
                originalContainerRGB = '255, 255, 255';
                originalSystemMessageRGB = '52, 58, 64';
                originalUserMessageRGB = '74, 144, 226';
                originalInputAreaRGB = '13, 17, 21';
                originalChatSendButtonRGB = '33, 37, 41';
                break;
            case 'blue':
                els.chatModal.style.backgroundColor = `rgba(224, 247, 250, ${opacity})`;
                els.chatModalheader.style.backgroundColor = `rgba(74, 144, 226, ${opacity})`;
                els.chatModalheader.style.color = '#fff';
                els.chatContainer.style.color = '#333';
                els.chatInputArea.style.backgroundColor = `rgba(74, 144, 226, ${opacity})`;
                els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(224, 247, 250)`;
                // els.sendMessageButtonInChatModal.style.color = '#fff';
                // 按钮文本颜色黑色
                els.sendMessageButtonInChatModal.style.color = '#333';
                systemBg = 'rgba(240, 248, 255, 0.9)';
                systemColor = '#333';
                userBg = 'rgba(74, 144, 226, 0.9)';
                userColor = '#fff';
                // Save original RGB values
                originalModalRGB = '224, 247, 250';
                originalHeaderRGB = '74, 144, 226';
                originalContainerRGB = '51, 51, 51';
                originalSystemMessageRGB = '240, 248, 255';
                originalUserMessageRGB = '74, 144, 226';
                originalInputAreaRGB = '74, 144, 226';
                originalChatSendButtonRGB = '224, 247, 250';
                // 创建新的 SVG URL，使用当前主题颜色
                aiAvatarSvg = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='rgba(74, 144, 226, 0.9)'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3EAI%3C/text%3E%3C/svg%3E`;

                // 更新所有 AI 头像
                aiAvatars = document.querySelectorAll('.chat-item:not(.user) .avatar');
                aiAvatars.forEach(avatar => {
                    avatar.src = aiAvatarSvg;
                });
                break;
            case 'green':
                els.chatModal.style.backgroundColor = `rgba(232, 245, 233, ${opacity})`;
                els.chatModalheader.style.backgroundColor = `rgba(76, 175, 80, ${opacity})`;
                els.chatModalheader.style.color = '#fff';
                els.chatContainer.style.color = '#333';
                els.chatInputArea.style.backgroundColor = `rgba(76, 175, 80, ${opacity})`;
                els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(232, 245, 233)`;
                // 按钮文本颜色黑色
                els.sendMessageButtonInChatModal.style.color = '#333';
                systemBg = 'rgba(240, 248, 240, 0.9)';
                systemColor = '#333';
                userBg = 'rgba(76, 175, 80, 0.9)';
                userColor = '#fff';
                // Save original RGB values
                originalModalRGB = '232, 245, 233';
                originalHeaderRGB = '76, 175, 80';
                originalContainerRGB = '51, 51, 51';
                originalSystemMessageRGB = '240, 248, 240';
                originalUserMessageRGB = '76, 175, 80';
                originalInputAreaRGB = '76, 175, 80';
                originalChatSendButtonRGB = '232, 245, 233';
                // 创建新的 SVG URL，使用当前主题颜色
                aiAvatarSvg = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='rgba(76, 175, 80, 0.9)'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3EAI%3C/text%3E%3C/svg%3E`;

                // 更新所有 AI 头像
                aiAvatars = document.querySelectorAll('.chat-item:not(.user) .avatar');
                aiAvatars.forEach(avatar => {
                    avatar.src = aiAvatarSvg;
                });
                break;
            case 'reset':
                els.chatModal.style.cssText = '';
                els.chatModal.className = 'chat-modal';
                els.chatModal.style.position = 'absolute';
                const computedStyle = window.getComputedStyle(els.chatModal);
                const width = parseInt(computedStyle.width) || 0;
                const height = parseInt(computedStyle.height) || 0;
                const rightValue = parseInt(computedStyle.right) || 110;
                const bottomValue = parseInt(computedStyle.bottom) || 30;
                // 计算left和top值
                const newLeft = window.innerWidth - width - rightValue;
                const newTop = window.innerHeight - height - bottomValue;
                // 设置新的left和top值
                els.chatModal.style.left = newLeft + 'px';
                els.chatModal.style.top = newTop + 'px';
                // 正确覆盖right和bottom属性
                els.chatModal.style.right = 'auto';
                els.chatModal.style.bottom = 'auto';
                els.chatModal.style.width = '400px';
                els.chatModal.style.height = '300px';
                // 保持显示对话框
                if (!els.chatModal.classList.contains('show')) {
                    els.chatModal.classList.add('show');
                }
                els.chatModalheader.style.cssText = '';
                els.chatContainer.style.color = '';
                els.chatInputArea.style.backgroundColor = `rgba(255, 255, 255, ${opacity})`;
                els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(74, 144, 226)`;
                // 按钮文本颜色白色
                els.sendMessageButtonInChatModal.style.color = '#fff';
                systemBg = 'rgba(240, 240, 240, 0.9)';
                systemColor = '#333';
                userBg = 'rgba(74, 144, 226, 0.9)';
                userColor = '#fff';
                // Save original RGB values
                originalModalRGB = '255, 255, 255';
                originalHeaderRGB = '74, 144, 226';
                originalContainerRGB = '51, 51, 51';
                originalSystemMessageRGB = '240, 240, 240';
                originalUserMessageRGB = '74, 144, 226';
                originalInputAreaRGB = '255, 255, 255';
                originalChatSendButtonRGB = '74, 144, 226';
                // 让 chatModal 显示
                // 创建新的 SVG URL，使用当前主题颜色
                aiAvatarSvg = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='rgba(74, 144, 226, 0.9)'/%3E%3Ctext x='20' y='26' text-anchor='middle' fill='white' font-size='16' font-weight='bold'%3EAI%3C/text%3E%3C/svg%3E`;

                // 更新所有 AI 头像
                aiAvatars = document.querySelectorAll('.chat-item:not(.user) .avatar');
                aiAvatars.forEach(avatar => {
                    avatar.src = aiAvatarSvg;
                });

                chatModalOpacity = updateSliderAndInput(els.chatModalOpacitySlider, els.chatModalOpacityValue, 1);
                break;
        }

        // 更新所有现有消息的样式
        const systemMessages = document.querySelectorAll('.chat-item:not(.user) .chat-message');
        const userMessages = document.querySelectorAll('.chat-item.user .chat-message');
        
        systemMessages.forEach(msg => {
            msg.style.backgroundColor = systemBg;
            msg.style.color = systemColor;
        });
        
        userMessages.forEach(msg => {
            msg.style.backgroundColor = userBg;
            msg.style.color = userColor;
        });
    }

    // 切换标签
    function switchTab(modalType, targetTab, clickedButton) {
        // 获取相关的所有元素
        const tabButtons = document.querySelector(`.tabs[data-modal="${modalType}"]`)
            .querySelectorAll('.tab-button');
        const tabContents = document.querySelector(`.modal-scroll-container[data-modal="${modalType}"]`)
            .querySelectorAll('.tab-content');
        
        // 移除所有活动状态
        tabButtons.forEach(btn => btn.classList.remove('active'));
        tabContents.forEach(content => content.classList.remove('active'));
        
        // 激活目标标签
        clickedButton.classList.add('active');
        document.getElementById(targetTab).classList.add('active');
        
        // 如果需要，可以添加额外的回调或动画
        if (typeof onTabSwitch === 'function') {
            onTabSwitch(modalType, targetTab);
        }
    }
    
    // 可选：添加标签切换的回调函数
    function onTabSwitch(modalType, targetTab) {
        // 处理特定标签切换后的逻辑
        if (modalType === 'settings') {
            if (targetTab === 'tab-chat') {
                // 处理切换到聊天标签的逻辑
            }
        } else if (modalType === 'test') {
            if (targetTab === 'tab-dev') {
                // 处理切换到开发者选项标签的逻辑
            }
        }
    }

    function openMediaFrameAndPositionDH() {
        // 打开媒体框
        const mediaPosition = openMediaFrame(techMediaPositionMode);
        // 数字人随媒体框位置变化，
        // 数字人随媒体框位置变化，
        if (mediaPosition === 1) {
            // 数字人左上角
            // const precisionPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'right-bottom', 0, 0, 0.1);
            // dhPreviousSizeAndPosition = {
            //     previousWidth: precisionPosition.width,
            //     previousHeight: precisionPosition.height,
            //     previousLeft: precisionPosition.previousLeft,
            //     previousTop: precisionPosition.previousTop,
            // }
            if (window.innerWidth < 768) {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'center-bottom', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            } else {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'right-bottom', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            }
            // 将toggleMediaFrame的文本改为关闭媒体框
        } else if (mediaPosition === 2) {
            // 数字人右上角
            // const precisionPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'left-bottom', 0, 0, 0.1);
            // dhPreviousSizeAndPosition = {
            //     previousWidth: precisionPosition.width,
            //     previousHeight: precisionPosition.height,
            //     previousLeft: precisionPosition.previousLeft,
            //     previousTop: precisionPosition.previousTop,
            // }
            if (window.innerWidth < 768) {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'center-bottom', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            } else {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'left-bottom', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            }
        } else if (mediaPosition === 3) {
            // 数字人顶部
            if (window.innerWidth >= 768) {
                // 随机左下或右下
                const random = Math.random();
                if (random < 0.5) {
                    dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'left-bottom', dhOffset, dhSize, dhOffsetElements, 0, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height, 0);
                } else {
                    dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'right-bottom', dhOffset, dhSize, dhOffsetElements, 0, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
                } 
            } else {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'center-bottom', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            }
        } else if (mediaPosition === 4) {
            // 数字人中部
            if (window.innerWidth >= 768) {
                // 随机左下或右下
                const random = Math.random();
                if (random < 0.5) {
                    dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'left-bottom', dhOffset, dhSize, dhOffsetElements, 0, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
                } else {
                    dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'right-bottom', dhOffset, dhSize, dhOffsetElements, 0, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
                }
            } else {
                dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'center', dhOffset, dhSize, dhOffsetElements, dhMarginXForTechMedia, dhMarginYForTechMedia, 0.1, null, dhSizeForTechMedia.width, dhSizeForTechMedia.height);
            }
        } else if (mediaPosition === 0) {
            // 恢复数字人位置
            console.log('恢复前数字人目标位置', dhPreviousSizeAndPosition);
            positionAndResizeElement(els.canvas, 'custom', dhOffset, dhSize, dhOffsetElements, 0, 0, 0.1, null, dhPreviousSizeAndPosition.previousWidth, dhPreviousSizeAndPosition.previousHeight, dhPreviousSizeAndPosition.previousLeft, dhPreviousSizeAndPosition.previousTop);
            console.log('恢复后数字人位置', dhPreviousSizeAndPosition);
        } 
        // 媒体框已打开
        console.log('媒体框已打开');
    }

    function openMediaFrame(positionMode = 6) {
        // 打开媒体框
        console.log('进入openMediaFrame方法');

        if (window.innerWidth < 768) {
            // 将所有的非3和4的转为3，4
            if (positionMode !== 3 && positionMode !== 4) {
                positionMode = 3;
            }
        }

        const frame = els.mediaFrame;
        
        // if (isAnimating) return;
        
        // if (!techMediaIsClosed) {
        //     return;
        // }
        // isAnimating = true;
        frame.classList.remove('active');
        frame.offsetHeight; // 强制重排
        
        frame.classList.remove('anim-scale', 'anim-rotate', 'anim-slide', 'anim-flip', 
                            'anim-bounce', 'anim-fade');
        frame.classList.add('anim-' + currentAnimation);
        
        frame.classList.remove('position-left', 'position-right', 'position-top', 'position-center', 'position-bottom');
        
        // 根据positionMode决定位置
        let actualPosition = positionMode; // 1代表左，2代表右
        
        switch(positionMode) {
            case 0: // 随机左右
                actualPosition = Math.random() > 0.5 ? 2 : 1;
                frame.classList.add(actualPosition === 2 ? 'position-right' : 'position-left');
                break;
            case 1: // 总是左边
                actualPosition = 1;
                frame.classList.add('position-left');
                break;
            case 2: // 总是右边
                actualPosition = 2;
                frame.classList.add('position-right');
                break;
            case 3: // 手机端顶部
                actualPosition = 3;
                frame.classList.add('position-top');
                break;
            case 4: // 手机端中部
                actualPosition = 4;
                frame.classList.add('position-center');
                break;
            case 5: // 手机端底部
                actualPosition = 5;
                frame.classList.add('position-bottom');
                break;
            case 6: // 左右交替(默认行为)
            default:
                actualPosition = positionToggle ? 2 : 1;
                frame.classList.add(positionToggle ? 'position-right' : 'position-left');
                positionToggle = !positionToggle; // 切换状态，为下次做准备
                break;
        }
        
        requestAnimationFrame(() => {
            frame.classList.add('active');
        });
        
        startParticleCascade();
        
        frame.addEventListener('transitionend', function handler() {
            // isAnimating = false;
            frame.removeEventListener('transitionend', handler);
        });

        console.log('打开媒体框定位方式：', positionMode, '实际位置：', actualPosition);

        return actualPosition; // 返回实际位置：1代表左，2代表右
    }

    function closeClearMeFAndDHReset() {
        closeAndClearMediaFrame()
        // 数字人恢复位置
        console.log('恢复前数字人目标位置', dhPreviousSizeAndPosition);
        positionAndResizeElement(els.canvas, 'custom', dhOffset, dhSize, dhOffsetElements, 0, 0, 0.1, null, dhPreviousSizeAndPosition.previousWidth, dhPreviousSizeAndPosition.previousHeight, dhPreviousSizeAndPosition.previousLeft, dhPreviousSizeAndPosition.previousTop);
        console.log('恢复后数字人目标位置', dhPreviousSizeAndPosition);
    }

    function closeAndClearMediaFrame( timeOut = techMediaPlaySettings.fadeOutTime) {
        // 关闭媒体框
        closeMediaFrame();
        // 延迟淡出动画长度再清空媒体框
        setTimeout(() => {
            resetUploadZone();
            console.log('清空媒体框');
        }, timeOut);
    }

    function closeMediaFrameAndResetDH() {
        closeMediaFrame()
        // 数字人恢复位置
        console.log('恢复前数字人目标位置', dhPreviousSizeAndPosition);
        positionAndResizeElement(els.canvas, 'custom', dhOffset, dhSize, dhOffsetElements, 0, 0, 0.1, null, dhPreviousSizeAndPosition.previousWidth, dhPreviousSizeAndPosition.previousHeight, dhPreviousSizeAndPosition.previousLeft, dhPreviousSizeAndPosition.previousTop);
        console.log('恢复后数字人目标位置', dhPreviousSizeAndPosition);
    }

    function closeMediaFrame() {
        // if (techMediaIsClosed) {
        //     return;
        // }
        if (!els.mediaFrame.classList.contains('active')) {
            return;
        }
        const frame = els.mediaFrame;
        
        // if (isAnimating) return;
        
        // isAnimating = true;
        frame.classList.remove('active');
        stopParticleCascade();
        
        if (currentAudio) {
            currentAudio.pause();
            isMediaPlaying = false;
        }
        
        frame.addEventListener('transitionend', function handler() {
            // isAnimating = false;
            frame.removeEventListener('transitionend', handler);
        });

        // techMediaIsClosed = true;
        console.log('媒体框关闭');
    }

    function applyAnimation(animationType) {
        const frame = els.mediaFrame;
        
        frame.classList.remove('anim-scale', 'anim-rotate', 'anim-slide', 'anim-flip', 
                            'anim-bounce', 'anim-fade');
        frame.classList.add('anim-' + animationType);
        currentAnimation = animationType;
    }

    function startParticleCascade() {
        const cascade = els.particleCascade;
        
        function createParticle() {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDuration = (Math.random() * 3 + 2) + 's';
            particle.style.animationDelay = Math.random() * 2 + 's';
            
            cascade.appendChild(particle);
            
            setTimeout(() => {
                if (particle.parentNode) {
                    particle.remove();
                }
            }, 5000);
        }
        
        particleInterval = setInterval(createParticle, 200);
    }

    function stopParticleCascade() {
        if (particleInterval) {
            clearInterval(particleInterval);
            particleInterval = null;
        }
        
        const particles = document.querySelectorAll('.particle');
        particles.forEach(particle => particle.remove());
    }

    function handleFileSelect(event) {
        console.log('通过点击上传触发了 handleFileSelect。事件对象:', event); 
        const file = event.target.files[0];
        console.log('选择的文件:', file); 
        if (file) {
            // 标记为手动上传的媒体
            displayAndPlayMedia(file, true);
        } else {
            console.log('没有选择文件，或者 event.target.files 为空。');
        }
    }

    function displayAndPlayMedia(fileSrc, isManualUpload = false) {
        const mediaContent = els.mediaContent;
        const fileType = fileSrc.type.split('/')[0];

        if (fileType === 'image') {
            displayImage(fileSrc, mediaContent, isManualUpload);
        } else if (fileType === 'video') {
            const video = displayAndPlayVideo(fileSrc, mediaContent, isManualUpload);
            if (video) {
                startMediaSpeakerDetection(video);
            }
        } else if (fileType === 'audio') {
            // 查找其下的audio标签
            const audio = displayAndPlayAudio(fileSrc, mediaContent, isManualUpload);
            if (audio) {
                startMediaSpeakerDetection(audio);
            }
        }
    }

    function displayMedia(fileSrc, isManualUpload = false) {
        const mediaContent = els.mediaContent;
        const fileType = fileSrc.type.split('/')[0];

        if (fileType === 'image') {
            displayImage(fileSrc, mediaContent, isManualUpload);
        } else if (fileType === 'video') {
            const video = displayVideo(fileSrc, mediaContent, isManualUpload);
            if (video) {
                startMediaSpeakerDetection(video);
            }
        } else if (fileType === 'audio') {
            const audio = displayAudio(fileSrc, mediaContent, isManualUpload);
            if (audio) {
                startMediaSpeakerDetection(audio);
            }
        }
    }

    function displayImage(imageSrc, container, isManualUpload = false) {
        const img = document.createElement('img');
        img.className = 'media-item fill';
        
        // 智能判断参数类型
        if (typeof imageSrc === 'string') {
            // 如果是URL字符串，直接设置src
            img.src = imageSrc;
            // 对于URL，不需要在加载后释放资源
        } else if (imageSrc instanceof File || imageSrc instanceof Blob) {
            // 如果是File或Blob对象，使用createObjectURL
            img.src = URL.createObjectURL(imageSrc);
            // 加载后释放对象URL
            img.onload = () => {
                URL.revokeObjectURL(img.src);
                const ratio = img.naturalWidth / img.naturalHeight;
                if (Math.abs(ratio - 16/9) > 0.1) {
                    img.classList.remove('fill');
                }
            };
        } else {
            console.error('不支持的图片源类型', imageSrc);
            return;
        }
        
        // 为URL字符串也添加宽高比例检查
        if (typeof imageSrc === 'string') {
            img.onload = () => {
                const ratio = img.naturalWidth / img.naturalHeight;
                if (Math.abs(ratio - 16/9) > 0.1) {
                    img.classList.remove('fill');
                }
            };
        }
        
        container.innerHTML = '';
        container.appendChild(img);
        
        // 判断是否启用自动关闭
        // 如果是手动上传且配置为不自动关闭手动上传内容，则不添加自动关闭
        if (techMediaPlaySettings.autoCloseEnabled && 
            !(isManualUpload && techMediaPlaySettings.autoCloseOnManualUpload === false)) {
            // 为图片添加定时关闭
            setTimeout(() => {
                if (techMediaPlaySettings.autoCloseOnUpload) {
                    closeClearMeFAndDHReset();
                } else {
                    closeMediaFrameAndResetDH();
                }
            }, techMediaPlaySettings.imageDisplayTime);
        }
    }

    function displayAndPlayVideo(videoSrc, container, isManualUpload = false) {
        return displayVideo(videoSrc, container, isManualUpload, true);
    }

    function displayVideo(videoSrc, container, isManualUpload = false, isAutoPlay = false) {
        const video = document.createElement('video');
        video.className = 'media-item fill';

        // 添加crossOrigin属性
        // video.crossOrigin = "anonymous";
        
        // 智能判断参数类型
        if (typeof videoSrc === 'string') {
            // 如果是字符串（URL），直接赋值
            video.src = videoSrc;
        } else if (videoSrc instanceof File || videoSrc instanceof Blob) {
            // 如果是File或Blob对象，使用createObjectURL
            video.src = URL.createObjectURL(videoSrc);
        } else {
            console.error('不支持的视频源类型', videoSrc);
            return;
        }
        
        // 移动端视频自动播放设置
        video.controls = true;
        // video.autoplay = isAutoPlay;
        // video.muted = isAutoPlay; // 静音视频可以自动播放
        video.playsInline = true; // 内联播放
        video.setAttribute('playsinline', ''); // iOS需要
        video.setAttribute('webkit-playsinline', ''); // 旧版iOS需要
        video.setAttribute('x5-playsinline', ''); // 腾讯X5内核需要
        video.setAttribute('x5-video-player-type', 'h5'); // 腾讯X5视频播放器类型
        video.setAttribute('x-webkit-airplay', 'allow'); // 允许AirPlay
        
        // 如果需要自动播放，在加载完成后尝试播放
        if (isAutoPlay) {
            video.oncanplay = function() {
                const playPromise = video.play();
                if (playPromise !== undefined) {
                    playPromise.catch(error => {
                        console.warn('自动播放失败，可能需要用户交互:', error);
                        // 显示一个播放按钮或提示用户点击播放
                    });
                }
            };
        }
        
        video.onloadedmetadata = () => {
            const ratio = video.videoWidth / video.videoHeight;
            if (Math.abs(ratio - 16/9) > 0.1) {
                video.classList.remove('fill');
            }
        };
        
        if (techMediaPlaySettings.autoCloseEnabled && 
            !(isManualUpload && techMediaPlaySettings.autoCloseOnManualUpload === false)) {
            video.onended = function() {
                // 使用延迟让用户有时间看到播放完成
                // setTimeout(() => {
                    if (techMediaPlaySettings.autoCloseOnUpload) {
                        closeClearMeFAndDHReset();
                    } else {
                        closeMediaFrameAndResetDH();
                    }
                // }, techMediaPlaySettings.fadeOutTime);
            };
        } 

        // 点击事件实现自动播放
        video.play();
        
        container.innerHTML = '';
        container.appendChild(video);
        return video;
    }

    function displayAndPlayAudio(url, container, isManualUpload = false) {
        
        const audioPlayerDom = displayAudio(url, container, isManualUpload);
        const playButton = audioPlayerDom.playButton;

        // 直接播放
        playButton.click();
        
        return audioPlayerDom.audio;
    }

    function displayAudio(file, container, isManualUpload = false) {
        const audioPlayerDom = createAudioPlayer(file);
        container.innerHTML = '';
        container.appendChild(audioPlayerDom.playerDiv);
        
        // 如果启用了自动关闭且不是手动上传（或配置允许自动关闭手动上传）
        if (techMediaPlaySettings.autoCloseEnabled && 
            !(isManualUpload && techMediaPlaySettings.autoCloseOnManualUpload === false)) {
            const audio = audioPlayerDom.audio;
            if (audio) {
                audio.onended = function() {
                    // 使用延迟让用户有时间看到播放完成
                    // setTimeout(() => {
                        if (techMediaPlaySettings.autoCloseOnUpload) {
                            closeClearMeFAndDHReset();
                        } else {
                            closeMediaFrameAndResetDH();
                        }
                    // }, techMediaPlaySettings.fadeOutTime);
                };
            }
        }
        
        return audioPlayerDom;
    }

    function createAudioPlayer(audioSrc) {
        const playerDiv = document.createElement('div');
        playerDiv.className = 'audio-player';
        
        // 创建音频元素
        const audio = document.createElement('audio');
        
        // 设置音频属性，强制使用扬声器
        audio.setAttribute('playsinline', '');
        audio.setAttribute('webkit-playsinline', '');
        audio.setAttribute('x5-playsinline', '');
        audio.setAttribute('x-webkit-airplay', 'allow');
        audio.setAttribute('x5-video-player-type', 'h5');
        
        // 设置高音量，避免系统自动切换到听筒
        audio.volume = 1.0;
        
        // 智能判断参数类型
        let fileName = '';
        
        if (typeof audioSrc === 'string') {
            // 如果是字符串（URL），直接赋值
            audio.src = audioSrc;
            // 从URL提取文件名
            fileName = audioSrc.split('/').pop().split('?')[0];
        } else if (audioSrc instanceof File || audioSrc instanceof Blob) {
            // 如果是File或Blob对象，使用createObjectURL
            audio.src = URL.createObjectURL(audioSrc);
            // 获取文件名
            fileName = audioSrc.name || '未知文件';
        } else {
            console.error('不支持的参数类型', audioSrc);
            return null;
        }
        
        audio.preload = 'metadata'; // 预加载元数据
        currentAudio = audio;

        // 创建可视化器
        const visualizer = document.createElement('div');
        visualizer.className = 'audio-visualizer';
        
        // 创建音频条
        for (let i = 0; i < 120; i++) {
            const bar = document.createElement('div');
            bar.className = 'bar';
            bar.style.height = `${Math.random() * 30 + 10}px`;
            visualizer.appendChild(bar);
        }

        // 创建控制器
        const controls = document.createElement('div');
        controls.className = 'audio-controls';
        
        const audioInfo = document.createElement('div');
        audioInfo.className = 'audio-info';
        
        // 处理文件名（移除扩展名）
        const displayName = fileName.split('.').slice(0, -1).join('.') || fileName;
        
        audioInfo.innerHTML = `
            <div class="audio-title">${displayName}</div>
            <div class="audio-duration">00:00 / 00:00</div>
        `;

        const progressContainer = document.createElement('div');
        progressContainer.className = 'media-progress-container';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'media-progress-bar';
        
        const progressHandle = document.createElement('div');
        progressHandle.className = 'media-progress-handle';
        
        progressContainer.appendChild(progressBar);
        progressContainer.appendChild(progressHandle);

        const controlButtons = document.createElement('div');
        controlButtons.className = 'control-buttons';
        
        const playButton = document.createElement('button');
        playButton.className = 'play-button';
        playButton.innerHTML = '<div class="play-icon"></div>';
        
        const timeDisplay = document.createElement('div');
        timeDisplay.className = 'time-display';
        timeDisplay.innerHTML = '<span>0:00</span><span>0:00</span>';

        controlButtons.appendChild(playButton);
        controls.appendChild(audioInfo);
        controls.appendChild(progressContainer);
        controls.appendChild(timeDisplay);
        controls.appendChild(controlButtons);

        playerDiv.appendChild(visualizer);
        playerDiv.appendChild(controls);
        playerDiv.appendChild(audio);

        // 存储需要的DOM元素引用
        const audioPlayer = {
            playerDiv,
            audio,
            playButton,
            progressContainer,
            progressBar,
            progressHandle,
            timeDisplay,
            audioInfo,
            visualizer,
            bars: visualizer.querySelectorAll('.bar')
        };

        // 添加事件监听器
        setupAudioPlayerEvents(audioPlayer);

        // 加载音频
        audio.load();

        return audioPlayer;
    }

    function setupAudioPlayerEvents(player) {
        let isDragging = false;

        player.playButton.addEventListener('click', () => togglePlayAudio(player));
        
        // 进度条拖动事件
        player.progressHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
            e.preventDefault();
        });

        // 触摸设备支持
        player.progressHandle.addEventListener('touchstart', (e) => {
            isDragging = true;
            document.addEventListener('touchmove', handleTouchDrag);
            document.addEventListener('touchend', stopDrag);
            e.preventDefault();
        });

        function handleDrag(e) {
            if (!isDragging) return;
            
            const rect = player.progressContainer.getBoundingClientRect();
            let percent = (e.clientX - rect.left) / rect.width;
            percent = Math.max(0, Math.min(1, percent));
            
            // 更新进度条显示
            updateProgress(player, percent);
            
            // 更新音频播放位置
            player.audio.currentTime = percent * player.audio.duration;
        }

        function handleTouchDrag(e) {
            if (!isDragging) return;
            
            const touch = e.touches[0];
            const rect = player.progressContainer.getBoundingClientRect();
            let percent = (touch.clientX - rect.left) / rect.width;
            percent = Math.max(0, Math.min(1, percent));
            
            // 更新进度条显示
            updateProgress(player, percent);
            
            // 更新音频播放位置
            player.audio.currentTime = percent * player.audio.duration;
        }

        function stopDrag() {
            if (!isDragging) return;
            isDragging = false;
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('touchmove', handleTouchDrag);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('touchend', stopDrag);
        }

        // 进度条点击事件
        player.progressContainer.addEventListener('click', (e) => {
            if (isDragging) return;
            handleProgressClick(e, player);
        });

        // 音频元数据加载完成事件
        player.audio.addEventListener('loadedmetadata', () => {
            updateDuration(player);
            // 初始化进度条
            updateProgress(player, 0);
        });

        // 音频播放时间更新事件
        player.audio.addEventListener('timeupdate', () => {
            if (!isDragging && player.audio.duration) {
                const percent = player.audio.currentTime / player.audio.duration;
                updateProgress(player, percent);
            }
        });
        
        // 音频播放结束事件
        player.audio.addEventListener('ended', () => {
            isMediaPlaying = false;
            player.playButton.innerHTML = '<div class="play-icon"></div>';
            stopVisualization(player);
            // 重置进度条
            updateProgress(player, 0);
        });

        // 音频加载错误事件
        player.audio.addEventListener('error', (e) => {
            console.error('音频加载失败:', e.target.error);
            isMediaPlaying = false;
            player.playButton.innerHTML = '<div class="play-icon"></div>';
        });
    }

    function handleProgressClick(e, player) {
        const rect = player.progressContainer.getBoundingClientRect();
        let percent = (e.clientX - rect.left) / rect.width;
        percent = Math.max(0, Math.min(1, percent));
        
        // 更新进度条显示
        updateProgress(player, percent);
        
        // 更新音频播放位置
        player.audio.currentTime = percent * player.audio.duration;
    }
    
    function togglePlayAudio(player) {
        if (isMediaPlaying) {
            console.log('暂停播放');
            player.audio.pause();
            isMediaPlaying = false;
            player.playButton.innerHTML = '<div class="play-icon"></div>';
            stopVisualization(player);
        } else {
            console.log('开始播放');
            player.audio.play()
                .then(() => {
                    console.log('播放成功');
                    isMediaPlaying = true;
                    player.playButton.innerHTML = '<div class="pause-icon"></div>';
                    startVisualization(player);
                })
                .catch(error => {
                    console.error('音频播放失败:', error);
                    isMediaPlaying = false;
                    player.playButton.innerHTML = '<div class="play-icon"></div>';
                });
        }
    }

    function updateDuration(player) {
        const duration = formatTime(player.audio.duration);
        player.timeDisplay.children[1].textContent = duration;
        player.audioInfo.children[1].textContent = `00:00 / ${duration}`;
    }

    function startVisualization(player) {
        let lastAnimationTime = 0;
        
        // 动态获取CSS中.bar元素的transition-duration
        const getBarTransitionDuration = () => {
            // 获取一个bar元素
            const barElement = player.bars[0];
            if (!barElement) return 200; // 默认值
        
            // 获取计算后的样式
            const computedStyle = window.getComputedStyle(barElement);
            const transitionDuration = computedStyle.getPropertyValue('transition-duration');
        
            // 分割多个过渡时间设置（以逗号分隔不同的过渡效果设置）
            const durations = transitionDuration.split(',');
            let maxDurationMs = 0;
            durations.forEach((durationStr) => {
                let durationMs = 200; // 默认值
                durationStr = durationStr.trim(); // 去除前后空格
                if (durationStr.includes('ms')) {
                    durationMs = parseFloat(durationStr);
                } else if (durationStr.includes('s')) {
                    durationMs = parseFloat(durationStr) * 1000;
                }
                maxDurationMs = Math.max(maxDurationMs, durationMs);
            });
        
            return maxDurationMs;
        };
        
        const animationDelay = getBarTransitionDuration();
        
        function animate(timestamp) {
            // 检查是否过了足够的时间进行下一次动画
            if (!lastAnimationTime || timestamp - lastAnimationTime >= animationDelay) {
                lastAnimationTime = timestamp;
                
                player.bars.forEach(bar => {
                    const height = Math.random() * 80 + 20;
                    bar.style.height = `${height}%`;
                });
            }
            
            if (isMediaPlaying) {
                animationFrame = requestAnimationFrame(animate);
            }
        }
        
        // 在开始新的动画之前，确保停止之前的动画
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
        
        // 启动动画
        animationFrame = requestAnimationFrame(animate);
    }

    function stopVisualization(player) {
        if (animationFrame) {
            cancelAnimationFrame(animationFrame);
        }
    }

    function formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    // 移除媒体框内的媒体，参数为清理事件绑定的方法
    function removeMediaFile(clearEvent) {
        // 清空媒体内容
        els.mediaContent.innerHTML = '';
        // 重置文件输入元素的值
        if (els.fileInput) {
            els.fileInput.value = '';
        }
        clearEvent();
    }

    // 添加uploadZone事件
    function addEventUploadZone() {
        // 拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            els.uploadZone.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            els.uploadZone.addEventListener(eventName, () => {
                if (els.uploadZone) els.uploadZone.classList.add('dragover');
            }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            els.uploadZone.addEventListener(eventName, () => {
                if (els.uploadZone) els.uploadZone.classList.remove('dragover');
            }, false);
        });

        // 拖放处理
        els.uploadZone.addEventListener('drop', (e) => {
            console.log('在上传区域触发了拖放事件。事件对象:', e); 
            const dt = e.dataTransfer;
            if (dt) {
                const files = dt.files;
                console.log('拖放的文件:', files); 
                if (files && files.length > 0) {
                    displayAndPlayMedia(files[0], true);
                } else {
                    console.log('没有文件被拖放，或者 dt.files 为空。');
                }
            } else {
                 console.log('拖放事件中的 e.dataTransfer 为空。');
            }
        });

        // 点击上传区域触发文件输入框
        els.uploadZone.addEventListener('click', () => {
            console.log('点击上传区域，创建文件选择器');
            
            // 触发文件选择
            console.log(els.fileInput)
            els.fileInput.click();
        });

    }
    
    // 确保 preventDefaults 只定义一次
    // 这个函数用于阻止拖拽事件的默认行为
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // 重新初始化uploadZone
    function resetUploadZone() {
        const clearEvent = () => {
            // 如果当前有音频在播放，停止它
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
                isMediaPlaying = false;
                
                // 如果有可视化动画在运行，停止它
                if (animationFrame) {
                    cancelAnimationFrame(animationFrame);
                    animationFrame = null;
                }
            }
        };
        
        // 调用removeMediaFile函数，传入清理事件函数
        removeMediaFile(clearEvent);
        
        // 显示上传区域
        const uploadZone = document.createElement('div');
        uploadZone.className = 'upload-zone';
        uploadZone.innerHTML = `
            <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                <polyline points="17,8 12,3 7,8"/>
                <line x1="12" y1="3" x2="12" y2="15"/>
            </svg>
            <div class="upload-text">拖拽文件到此处或点击上传</div>
            <div class="upload-hint">支持图片、视频和音频文件</div>
        `;

        updateElement('uploadZone', uploadZone);
        
        // 重新添加上传区域的点击事件
        addEventUploadZone();
        
        els.mediaContent.appendChild(uploadZone);
    }

    // ASR录音相关函数实现 
    /**
     * 初始化ASR录音器
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async function initASRRecorder() {
        try {
            
            try {
                // 请求麦克风权限
                await requestMicrophoneAccess();
            } catch (micError) {
                // 麦克风访问失败的特殊处理
                console.error("麦克风访问失败:", micError);
                
                // 根据错误类型提供更具体的提示
                if (micError.name === "NotFoundError") {
                    updateASRStatusHTML('<span style="color:#f72585">❌ 找不到麦克风设备，请检查麦克风连接</span>');
                    throw new Error("未检测到麦克风设备，请确认麦克风已正确连接并启用");
                } else if (micError.name === "NotAllowedError") {
                    updateASRStatusHTML('<span style="color:#f72585">❌ 麦克风权限被拒绝，请允许浏览器访问麦克风</span>');
                    throw new Error("麦克风访问权限被拒绝，请授予浏览器麦克风访问权限");
                } else if (micError.name === "AbortError") {
                    updateASRStatusHTML('<span style="color:#f72585">❌ 麦克风可能被其他应用占用</span>');
                    throw new Error("麦克风可能正被其他应用程序使用");
                } else {
                    updateASRStatusHTML(`<span style="color:#f72585">❌ 麦克风访问错误: ${micError.message || micError.name}</span>`);
                    throw new Error(`麦克风访问失败: ${micError.message || micError.name}`);
                }
            }


            // 初始化录音器
            const result = initRecorder({
                type: "pcm",
                sampleRate: 16000,
                bitRate: 16,
                onVolumeChange: (volume) => {
                    // 更新音量指示器（如有）
                    updateVolumeIndicator(volume);
                },
                onRecordStart: () => {
                    console.log("ASR录音开始");
                    updateASRStatusHTML("<span style='color:#2ec4b6'>正在录音...</span>");
                },
                onRecordStop: () => {
                    console.log("ASR录音停止");
                    updateASRStatusHTML("<span style='color:#ff9f1c'>录音已停止</span>");
                },
                onRecordError: (err) => {
                    console.log("ASR录音错误:", err);
                    updateASRStatusHTML(`<span style="color:#f72585">❌ 录音错误: ${err.message || '未知错误'}</span>`);
                },
                // 添加发送音频数据的回调
                onSendAudioData: (audioData, duration) => {
                    if (asrConnection && asrConnection.isConnected()) {
                        // 发送音频数据到ASR服务器
                        // console.log("发送音频数据到ASR服务器audioData: ", audioData);
                        asrConnection.send(audioData); // 发送ArrayBuffer而不是Int16Array
                        // updateASRStatusHTML(`<span style="color:#2ec4b6">语音识别中: ${(duration/1000).toFixed(1)}s</span>`);
                    }
                }
            });
            
            if (!result) {
                updateASRStatusHTML('<span style="color:#f72585">❌ 录音器初始化失败，请检查浏览器音频设置</span>');
                throw new Error("初始化录音器失败，请检查浏览器音频设置");
            }
            
            console.log("ASR录音器初始化成功");
            
            // 暂时关闭麦克风
            // closeMicrophone();
            return true;
        } catch (error) {
            console.error("初始化ASR录音器失败:", error);
            throw error; // 直接抛出原始错误，保留完整错误信息和堆栈
        }
    }

    async function initFaceDetectionModule() { 
        reloadFaceProfiles();

        if (faceDetectionEnabled && !faceDetectionModuleInitialized) {

            const baseURL = '.';

            // 初始化人脸检测模块
            const result = initFaceDetection({
                requireVerification: requireFaceVerification,
                detectionInterval: faceDetectionInterval,
                verificationThreshold: faceVerificationThreshold,
                autoSaveProfile: autoSaveProfile,
                tinyFaceDetectorModelURL: `${baseURL}/vendors/face-api/models/tiny_face_detector_model-weights_manifest.json`,
                faceLandmark68NetModelURL: `${baseURL}/vendors/face-api/models/face_landmark_68_model-weights_manifest.json`,
                faceRecognitionNetModelURL: `${baseURL}/vendors/face-api/models/face_recognition_model-weights_manifest.json`,
                onFaceDetected: async (detectionResult) => {
                    console.log("人脸检测成功:", detectionResult);
                    // 关闭人脸检测框
                    if (!requireFaceVerification) {
                        closeFaceDetection();
                    }

                    // 延迟1s后显示已唤醒
                    setTimeout(() => {
                        updateASRStatusHTML(`<span style="color:#06b6d4">✓ 数字人已唤醒，请开始对话</span>`);
                    }, 1000);

                    isWakeUp = true;

                    updateWakeupStatusHTML(`<span style="color:#06b6d4">✓ 数字人已唤醒</span>`);

                    // if (!currentFaceTextIsSend) {
                    //     await wakeupDetectionHandler(faceDetectionSendText);
                    // }

                    // 延迟启动录音，确保摄像头资源完全释放
                    setTimeout(() => {
                        safeStartRecord();
                    }, 3000);

                },
                onFaceVerified: async (match) => {
                    console.log(`身份验证成功用户：${match.label}`);
                    // 关闭人脸检测框
                    if (!requireFaceVerification) {
                        closeFaceDetection();
                    }
                    updateASRStatusHTML(`<span style="color:#06b6d4">✓ 已响应唤醒词: ${result.wakeWord.word}</span>`);
                    // 延迟1s后显示已唤醒
                    setTimeout(() => {
                        updateASRStatusHTML(`<span style="color:#06b6d4">✓ 数字人已唤醒，请开始对话</span>`);
                    }, 1000);

                    isWakeUp = true;

                    updateWakeupStatusHTML(`<span style="color:#06b6d4">✓ 数字人已唤醒</span>`);

                    if (!currentFaceTextIsSend) {
                        await wakeupDetectionHandler(faceDetectionSendText);
                    }
                },
                onFaceRejected: () => {
                    console.log("身份验证失败");
                    // 更新人脸检测状态
                },
                onFaceRegistered: (userFaceData) => {
                    console.log("人脸注册成功:", userFaceData);
                    // 更新人脸检测状态
                },
                onCameraOpened: (result) => {
                    console.log("摄像头打开成功:", result);
                    // 更新人脸检测状态
                },
                onCameraError: (error) => {
                    console.error("摄像头错误:", error);
                    // 更新人脸检测状态
                },
                onDrawDetection: (detectionResult) => {
                    if (detectionResult) {
                        console.log("人脸检测绘制:", detectionResult);
                    }
                    // 为空也绘制，作为清除绘制
                    handleFaceDrawing(detectionResult);
                },
                onStatusUpdate: (statusText, type) => {
                    console.log("人脸检测状态更新:", statusText, type);
                    // 更新人脸检测状态
                    updateFaceStatus(statusText, type);
                },
                onError: (error) => {
                    console.error("人脸检测错误:", error);
                },
                onTimeout: (result) => {
                    console.log("人脸检测超时:", result);
                    // 关闭人脸检测框
                    closeFaceDetection();
                }
            });
            
            if (!result) {
                updateASRStatusHTML('<span style="color:#f72585">❌ 初始化人脸检测模块失败，请检查浏览器音频设置</span>');
                throw new Error("初始化人脸检测模块失败，请检查浏览器音频设置");
            }
            faceDetectionModuleInitialized = true;
        }
        
    }

    async function wakeupDetectionHandler(wakeupText) {
        // 关闭人脸检测框
        els.chatInput.value = '';
        const res = await handleMessageSend(wakeupText);
        if (res && res.code === 0) {
            console.log('唤醒词响应成功:', res.data);
            // 发送成功后，清空输入框
            
            currentFaceTextIsSend = true;
        } else {
            console.log('唤醒词响应失败:', res.message);
        }
    }
    
    /**
     * 更新音量指示器
     * @param {number} volume 音量值（0-1）
     */
    function updateVolumeIndicator(volume) {
        // 如果有音量指示器UI元素，可以在这里更新
        // 这里可以根据实际情况实现
        // console.log("当前音量:", Math.round(volume * 100));
    }
    
    /**
     * 开始ASR录音
     * @returns {Promise<void>}
     */
    async function startASRRecording() {

        if (!asrConnection || !asrConnection.isConnected()) {
            updateASRStatusHTML('<span style="color:red">请先连接ASR服务</span>');
            return;
        }
        
        // 开始录音
        try {            
            // 清空结果区域
            if (els.asrResultArea) {
                els.asrResultArea.value = '';
            }
            
            // 开始录音
            const result = await startRecording();
            
            if (!result) {
                throw new Error("启动录音失败");
            }
            
            console.log("ASR录音开始");
            
            els.asrBtnStart.disabled = true;
            els.asrBtnStop.disabled = false;
            updateASRStatusHTML('<span style="color:green">正在录音并识别...</span>');

            return true;
        } catch (error) {
            console.error("开始ASR录音失败:", error);
            throw new Error(`开始录音失败: ${error.message || '未知错误'}`);
        }
           
    }
    
    /**
     * 停止ASR录音
     * @returns {Promise<void>}
     */
    async function stopASRRecording() {
        try {
            // 从utils/recorder.js导入需要的函数
            const { stopRecording } = await import('./utils/recorder.js');
            
            // 停止录音
            const result = await stopRecording();
            
            console.log("ASR录音停止");
            els.asrBtnStart.disabled = false;
            els.asrBtnStop.disabled = true;
            els.asrInfoElement.innerHTML = '<span style="color:#06b6d4">录音已停止</span>';
            return result;
        } catch (error) {
            console.error("停止ASR录音失败:", error);
            els.asrInfoElement.innerHTML = '<span style="color:red">停止录音失败: ' + error.message + '</span>';

        }
    }

    /**
     * 初始化人脸检测模块
     */
    async function initializeFaceDetectionModule() {
        
    }

    /**
     * 更新人脸检测状态显示
     */
    function updateFaceStatus(text, type) {
        if (!els.faceStatus) return;
        
        els.faceStatus.textContent = text;
        
        // 根据状态类型设置颜色
        switch (type) {
            case 'success':
                els.faceStatus.style.backgroundColor = 'rgba(0, 255, 0, 0.5)';
                break;
            case 'error':
                els.faceStatus.style.backgroundColor = 'rgba(255, 0, 0, 0.5)';
                break;
            case 'warning':
                els.faceStatus.style.backgroundColor = 'rgba(255, 165, 0, 0.5)';
                break;
            default:
                els.faceStatus.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        }
    }

    /**
     * 处理人脸检测绘制
     */
    function handleFaceDrawing(detectionResult) {
        if (!els.faceCanvas) return;
        
        // 确保视频和画布尺寸匹配
        if (els.faceVideo && els.faceCanvas) {
            // 获取视频的实际显示尺寸
            const videoRect = els.faceVideo.getBoundingClientRect();
            
            // 设置画布的显示尺寸与视频相同
            els.faceCanvas.style.width = `${videoRect.width}px`;
            els.faceCanvas.style.height = `${videoRect.height}px`;
            
            // 确保画布与视频元素完全重叠
            els.faceCanvas.style.position = 'absolute';
            els.faceCanvas.style.top = '0';
            els.faceCanvas.style.left = '0';
        }
        
        // 绘制检测结果
        drawFaceDetections(els.faceCanvas, detectionResult);
    }


    /**
     * 保存人脸检测配置
     */
    function saveFaceDetectionConfig() {
        try {
            const config = {
                requireVerification: requireFaceVerification,
                detectionInterval: faceDetectionInterval,
                verificationThreshold: faceVerificationThreshold
            };
            setStorage(STORAGE_NAMESPACE.FACE_CONFIG, config);
            
            // 更新模块配置
            setDetectionInterval(faceDetectionInterval);
            setVerificationThreshold(faceVerificationThreshold);
        } catch (e) {
            console.error('保存人脸检测配置失败:', e);
        }
    }

    /**
     * 注册当前人脸
     */
    async function handleFaceRegister() {
        const profileName = els.faceProfileName ? els.faceProfileName.value.trim() : '';
        
        if (!profileName) {
            showToast('请输入人脸配置文件名称', 'warning');
            return;
        }
        
        try {
            const success = await registerCurrentFace(profileName, els.faceVideo);
            if (success) {
                showToast(`已成功注册人脸: ${profileName}`, 'success');
                
                // 关闭对话框
                if (els.faceRegisterDialog) {
                    els.faceRegisterDialog.style.display = 'none';
                }
                
                // 清空输入
                if (els.faceProfileName) {
                    els.faceProfileName.value = '';
                }
            } else {
                showToast('注册失败，请确保摄像头可见您的脸部', 'error');
            }
        } catch (error) {
            console.error('注册人脸失败:', error);
            showToast('注册失败: ' + error.message, 'error');
        }
    }

    /**
     * 打开人脸检测框
     * @returns {Promise<boolean>} 显示是否成功
     */
    async function openFaceDetection() {
        try {
            // 打开摄像头
            const stream = await openCamera(els.faceVideo);
            if (!stream) return false;
            
            // 将视频流设置到视频元素
            if (els.faceVideo) {
                els.faceVideo.srcObject = stream;
                
                // 等待视频元数据加载
                await new Promise(resolve => {
                    els.faceVideo.onloadedmetadata = resolve;
                });
                
                // 开始播放视频
                await els.faceVideo.play();
            }
            

            // 确保使用正确的验证设置
            // 在唤醒流程中，我们需要进行身份验证，无论全局设置如何
            startDetectionLoop(els.faceVideo, requireFaceVerification, 30000);
            
            // 更新配置
            saveFaceDetectionConfig();
            
            // 更新ASR状态
            updateASRStatusHTML(`<span style="color:#06b6d4">⏱️ 正在进行人脸验证，请看向摄像头...</span>`);
            
            // 显示检测框
            els.faceDetectionContainer.style.display = 'block';

            isFaceVerifying = true;

            // 开始计时关闭人脸检测
            setTimeout(() => {
                const isClosed = closeFaceDetection();
                if (isClosed) {
                    updateASRStatusHTML(`<span style="color:#ef4444">❌ 人脸验证超时，请重新验证</span>`);
                }
            }, faceVerificationTimeout);

            return true;
        } catch (error) {
            console.error('显示人脸检测框失败:', error);
            updateASRStatusHTML(`<span style="color:#ef4444">❌ 显示人脸检测框失败</span>`);
            return false;
        }
    }

    /**
     * 隐藏人脸检测框
     */
    function closeFaceDetection(timeOut = 0) {
        if (!isFaceVerifying) {
            return false;
        }
        // 延迟关闭
        setTimeout(() => {
            // 隐藏容器
            if (els.faceDetectionContainer) {
                els.faceDetectionContainer.style.display = 'none';
            }
        
            // 停止检测
            stopFaceDetection();
            
            // 关闭摄像头
            closeCamera();
            
            saveFaceDetectionConfig();

            isFaceVerifying = false;
        }, timeOut);
        return true;
    }

    /**
     * 清除所有人脸数据
     */
    function clearFaceProfiles() {
        if (confirm('确定要清除所有已保存的人脸数据吗？此操作不可恢复。')) {
            clearAllFaceProfiles();
            showToast('已清除所有人脸数据', 'success');
        }
    }

    function closeAllModals() {
        // 先关闭所有的模态框
        closeChatModal();
        closeSettingsModal();
        closeTestModal();
    }

    function closeChatModal() {
        const chatModal = els.chatModal;
        if (chatModal.classList.contains('show')) {
            chatModal.classList.remove('show');
        }
    }

    function closeSettingsModal() {
        const settingsModal = els.settingsModal;
        const settingsModalToggle = els.settingsModalToggle;
        if (settingsModal) {
            // 如果当前是展开状态，先显示元素，然后再添加expanded类
            if (settingsModal.classList.contains('expanded')) {
                // 移除expanded类，触发收起动画
                settingsModal.classList.remove('expanded');
                settingsModalToggle.classList.remove('expanded');
                
                // 延迟隐藏，等待关闭动画完成
                setTimeout(() => {
                    if (!settingsModal.classList.contains('expanded')) {
                        settingsModal.style.display = 'none';
                    }
                }, 300);
            }
        }
    }

    function closeTestModal() {
        const testModal = els.testModal;
        if (testModal.classList.contains('show')) {
            testModal.classList.remove('show');
        }
    }

    
    function startMediaSpeakerDetection(mediaElement) {
        // 使用节流函数限制事件处理频率
        let lastProcessTime = 0;
        const throttleInterval = 200; // 每200ms最多处理一次
        
        mediaElement.addEventListener('timeupdate', function () {
            const now = Date.now();
            if (now - lastProcessTime < throttleInterval) return;
            lastProcessTime = now;
            
            if (mediaElement.paused && !userCloseTechMediaSpeaking) {
                console.log('视频处于暂停状态');
                setTimeout(() => {
                    resumeRecording();
                    updateASRStatusHTML(`<span style="color:#ff9f1c">⚠️ 检测到视频声音暂停，录音已恢复</span>`);
                }, speakerHoldTime);
            } else if (!userCloseTechMediaSpeaking) {
                pauseRecording();
                updateASRStatusHTML(`<span style="color:#2ec4b6"> 检测到视频声音播放，录音已暂停</span>`);
            }
        });
    }

    // 加载知识库数据
    function loadKnowledgeData() {
        try {
            // 加载已保存的数据库连接
            const savedConnections = getStorage(STORAGE_NAMESPACE.DB_CONNECTIONS);
            if (savedConnections) {
                dbConnections = JSON.parse(savedConnections);
                renderConnectionsList();
            }
            
            // 加载已上传的知识文件信息
            const savedFiles = getStorage(STORAGE_NAMESPACE.KNOWLEDGE_FILES);
            if (savedFiles) {
                uploadedKnowledgeFiles = JSON.parse(savedFiles);
                renderKnowledgeFileList();
                updateFileButtonsState();
            }
        } catch(error) {
            console.error('加载知识库数据失败:', error);
            showToast('加载知识库数据失败', 'error');
        }
    }

    // 获取表单数据
    function getConnectionFormData() {
        return {
            url: els.dbUrl.value.trim(),
            dbName: els.dbName.value.trim(),
            username: els.dbUsername.value.trim(),
            password: els.dbPassword.value.trim(),
            sessionid: parseInt(els.sessionId.value), // 生成唯一ID
        };
    }

    function validateConnectionData(data) {
        if (!data.url) {
            showToast('请输入数据库URL', 'error');
            return false;
        }
        if (!data.dbName) {
            showToast('请输入数据库名称', 'error');
            return false;
        }
        return true;
    }

    // 测试数据库连接
    async function testDatabaseConnection(connectionData) {
        showToast('正在测试连接...', 'info');
        
        try {
            // 构建测试连接的请求参数
            const testUrl = '/api/knowledge/test-connection';
            
            // 发送请求测试连接
            const response = await httpPost(testUrl, connectionData);
            
            if (response && response.success) {
                showToast('连接测试成功!', 'success');
                return true;
            } else {
                showToast(`连接测试失败: ${response.message || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            console.error('测试数据库连接出错:', error);
            showToast(`连接测试失败: ${error.message || '请求出错'}`, 'error');
            return false;
        }
    }

    // 解析MySQL配置
    function parseMySQLConfig(connectionData) {
        const config = {
            host: 'localhost',
            port: 3306,
            username: connectionData.username || '',
            password: connectionData.password || '',
            database: connectionData.dbName || ''
        };
        
        // 解析URL格式
        if (connectionData.url) {
            if (connectionData.url.startsWith('mysql://')) {
                // mysql://user:pass@host:port/db
                const regex = /^mysql:\/\/(?:([^:]+):([^@]+)@)?([^:\/]+)(?::(\d+))?(?:\/(.+))?$/;
                const match = connectionData.url.match(regex);
                
                if (match) {
                    config.username = match[1] || config.username;
                    config.password = match[2] || config.password;
                    config.host = match[3];
                    config.port = match[4] ? parseInt(match[4]) : 3306;
                    config.database = match[5] || config.database;
                }
            } else {
                // 简单的 host:port 格式
                const [host, port] = connectionData.url.split(':');
                config.host = host;
                config.port = port ? parseInt(port) : 3306;
            }
        }
        
        return config;
    }

    // 保存数据库连接
    function saveConnection(connectionData) {
        // 检查是否已存在相同URL和数据库名的连接
        const existingIndex = dbConnections.findIndex(
            conn => conn.url === connectionData.url && conn.dbName === connectionData.dbName
        );
        
        if (existingIndex >= 0) {
            // 更新现有连接
            dbConnections[existingIndex] = {
                ...dbConnections[existingIndex],
                ...connectionData,
                updatedAt: new Date().toISOString()
            };
            showToast('已更新连接设置', 'success');
        } else {
            // 添加新连接
            dbConnections.push(connectionData);
            showToast('已保存连接设置', 'success');
        }
        
        // 持久化保存
        setStorage(STORAGE_NAMESPACE.DB_CONNECTIONS, JSON.stringify(dbConnections));
        
        // 更新连接列表显示
        renderConnectionsList();
    }

    // 渲染连接列表
    function renderConnectionsList() {
        const listEl = els.connectionList;
        
        // 清空当前列表
        listEl.innerHTML = '';
        
        if (dbConnections.length === 0) {
            listEl.innerHTML = '<p class="empty-list-message" style="text-align: center; color: var(--text-secondary);">暂无保存的连接</p>';
            return;
        }
        
        // 创建连接列表项
        dbConnections.forEach((connection, index) => {
            const connectionItem = document.createElement('div');
            connectionItem.className = 'connection-item';
            connectionItem.style.padding = '8px';
            connectionItem.style.borderBottom = '1px solid #eee';
            connectionItem.style.display = 'flex';
            connectionItem.style.justifyContent = 'space-between';
            connectionItem.style.alignItems = 'center';
            
            const connectionInfo = document.createElement('div');
            connectionInfo.innerHTML = `
                <strong>${connection.dbName}</strong>
                <div style="font-size: 12px; color: var(--text-secondary);">${connection.url}</div>
            `;
            
            const actionButtons = document.createElement('div');
            
            // 加载按钮
            const loadBtn = document.createElement('button');
            loadBtn.type = 'button';
            loadBtn.className = 'btn-small';
            loadBtn.innerHTML = '加载';
            loadBtn.onclick = () => loadConnection(index);
            
            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn-small btn-danger';
            deleteBtn.style.marginLeft = '5px';
            deleteBtn.innerHTML = '删除';
            deleteBtn.onclick = () => deleteConnection(index);
            
            actionButtons.appendChild(loadBtn);
            actionButtons.appendChild(deleteBtn);
            
            connectionItem.appendChild(connectionInfo);
            connectionItem.appendChild(actionButtons);
            
            listEl.appendChild(connectionItem);
        });
    }

    // 加载连接到表单
    function loadConnection(index) {
        if (index >= 0 && index < dbConnections.length) {
            const connection = dbConnections[index];
            els.dbUrl.value = connection.url || '';
            els.dbName.value = connection.dbName || '';
            els.dbUsername.value = connection.username || '';
            els.dbPassword.value = connection.password || '';
            
            showToast('已加载连接数据', 'success');
        }
    }

    // 删除连接
    function deleteConnection(index) {
        if (index >= 0 && index < dbConnections.length) {
            dbConnections.splice(index, 1);
            setStorage(STORAGE_NAMESPACE.DB_CONNECTIONS, JSON.stringify(dbConnections));
            renderConnectionsList();
            showToast('已删除连接', 'success');
        }
    }

    // 提交连接到服务器
    async function submitConnection(connectionData) {
        showToast('正在提交连接...', 'info');
        
        try {
            // 构建提交连接的请求参数
            const submitUrl = '/api/knowledge/submit-connection';
            
            // 发送请求提交连接
            const response = await httpPost(submitUrl, connectionData);
            
            if (response && response.success) {
                showToast('连接提交成功!', 'success');
                return true;
            } else {
                showToast(`连接提交失败: ${response.message || '未知错误'}`, 'error');
                return false;
            }
        } catch (error) {
            console.error('提交数据库连接出错:', error);
            showToast(`连接提交失败: ${error.message || '请求出错'}`, 'error');
            return false;
        }
    }

    // 处理文件上传
    async function handleFileUpload(files) {
        if (!files || files.length === 0) {
            showToast('请选择文件', 'error');
            return;
        }
        
        showToast('正在上传文件...', 'info');
        
        // 创建FormData对象
        const formData = new FormData();
        
        // 添加所有选择的文件
        for (let i = 0; i < files.length; i++) {
            formData.append('files', files[i]);
        }
        
        try {
            // 上传文件
            const uploadUrl = '/api/knowledge/upload-files';
            const response = await upload(uploadUrl, formData, {
                onProgress: (progress) => {
                    // 如果需要，这里可以添加上传进度的处理
                    console.log(`上传进度: ${progress}%`);
                }
            });
            
            if (response && response.success) {
                // 更新上传的文件列表
                if (response.files && Array.isArray(response.files)) {
                    response.files.forEach(file => {
                        uploadedKnowledgeFiles.push({
                            id: file.id || Date.now().toString(),
                            name: file.name,
                            size: file.size,
                            type: file.type,
                            status: 'uploaded',
                            url: file.url || '',
                            uploadedAt: new Date().toISOString()
                        });
                    });
                    
                    // 保存到本地存储
                    setStorage(STORAGE_NAMESPACE.KNOWLEDGE_FILES, JSON.stringify(uploadedKnowledgeFiles));
                    
                    // 更新文件列表显示
                    renderKnowledgeFileList();
                    updateFileButtonsState();
                }
                
                showToast('文件上传成功!', 'success');
            } else {
                showToast(`文件上传失败: ${response.message || '未知错误'}`, 'error');
            }
        } catch (error) {
            console.error('上传文件出错:', error);
            showToast(`文件上传失败: ${error.message || '请求出错'}`, 'error');
        }
    }

    // 渲染知识文件列表
    function renderKnowledgeFileList() {
        const listEl = els.knowledgeFileList;
        
        // 清空当前列表
        listEl.innerHTML = '';
        
        if (uploadedKnowledgeFiles.length === 0) {
            listEl.innerHTML = '<p class="empty-list-message" style="text-align: center; color: var(--text-secondary);">暂无上传文件</p>';
            return;
        }
        
        // 创建文件列表项
        uploadedKnowledgeFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.style.padding = '8px';
            fileItem.style.borderBottom = '1px solid #eee';
            fileItem.style.display = 'flex';
            fileItem.style.justifyContent = 'space-between';
            fileItem.style.alignItems = 'center';
            
            // 获取文件图标
            let fileIcon = '📄';
            if (file.name.endsWith('.pdf')) fileIcon = '📕';
            else if (file.name.endsWith('.doc') || file.name.endsWith('.docx')) fileIcon = '📘';
            else if (file.name.endsWith('.ppt') || file.name.endsWith('.pptx')) fileIcon = '📙';
            else if (file.name.endsWith('.xls') || file.name.endsWith('.xlsx')) fileIcon = '📗';
            else if (file.name.endsWith('.db') || file.name.endsWith('.sql')) fileIcon = '🗃️';
            
            // 格式化文件大小
            const formatFileSize = (bytes) => {
                if (bytes < 1024) return bytes + ' B';
                else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
                else return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
            };
            
            const fileInfo = document.createElement('div');
            fileInfo.style.display = 'flex';
            fileInfo.style.alignItems = 'center';
            fileInfo.innerHTML = `
                <span style="font-size: 24px; margin-right: 10px;">${fileIcon}</span>
                <div>
                    <div style="font-weight: 500;">${file.name}</div>
                    <div style="font-size: 12px; color: var(--text-secondary);">
                        ${formatFileSize(file.size)} • ${new Date(file.uploadedAt).toLocaleString()}
                    </div>
                </div>
            `;
            
            const actionButtons = document.createElement('div');
            
            // 删除按钮
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn-small btn-danger';
            deleteBtn.innerHTML = '删除';
            deleteBtn.onclick = () => deleteFile(index);
            
            actionButtons.appendChild(deleteBtn);
            
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(actionButtons);
            
            listEl.appendChild(fileItem);
        });
    }

    // 删除文件
    function deleteFile(index) {
        if (index >= 0 && index < uploadedKnowledgeFiles.length) {
            // 如果文件已上传到服务器，可以发送请求删除服务器上的文件
            const file = uploadedKnowledgeFiles[index];
            
            if (file.url) {
                // 可选：发送请求删除服务器上的文件
                httpDel(`/api/knowledge/delete-file/${file.id}`).catch(error => {
                    console.error('删除服务器文件失败:', error);
                });
            }
            
            // 从本地列表中删除
            uploadedKnowledgeFiles.splice(index, 1);
            setStorage(STORAGE_NAMESPACE.KNOWLEDGE_FILES, JSON.stringify(uploadedKnowledgeFiles));
            renderKnowledgeFileList();
            updateFileButtonsState();
            showToast('已删除文件', 'success');
        }
    }

    // 处理文件
    async function processFiles() {
        if (uploadedKnowledgeFiles.length === 0) {
            showToast('没有可处理的文件', 'error');
            return;
        }
        
        showToast('正在处理文件...', 'info');
        
        // 收集所有文件的ID
        const fileIds = uploadedKnowledgeFiles.map(file => file.id);
        
        try {
            // 发送处理请求
            const processUrl = '/api/knowledge/process-files';
            const response = await httpPost(processUrl, { fileIds });
            
            if (response && response.success) {
                showToast('文件处理成功!', 'success');
                
                // 更新文件状态
                uploadedKnowledgeFiles = uploadedKnowledgeFiles.map(file => ({
                    ...file,
                    status: 'processed',
                    processedAt: new Date().toISOString()
                }));
                
                // 保存到本地存储并更新显示
                setStorage(STORAGE_NAMESPACE.KNOWLEDGE_FILES, JSON.stringify(uploadedKnowledgeFiles));
                renderKnowledgeFileList();
            } else {
                showToast(`文件处理失败: ${response.message || '未知错误'}`, 'error');
            }
        } catch (error) {
            console.error('处理文件出错:', error);
            showToast(`文件处理失败: ${error.message || '请求出错'}`, 'error');
        }
    }

    // 清空文件列表
    function clearFilesList() {
        uploadedKnowledgeFiles = [];
        setStorage(STORAGE_NAMESPACE.KNOWLEDGE_FILES, JSON.stringify(uploadedKnowledgeFiles));
        renderKnowledgeFileList();
        updateFileButtonsState();
        showToast('已清空文件列表', 'success');
    }

    // 更新文件按钮状态
    function updateFileButtonsState() {
        const hasFiles = uploadedKnowledgeFiles.length > 0;
        els.processFilesBtn.disabled = !hasFiles;
        els.clearFilesBtn.disabled = !hasFiles;
    }


    //#endregion

    // 数字人基础板块初始化方法
    function initializeDHModules() {
        initStorages();
        initializeDHVariables();
        // initASRRecorder();
        initFaceDetectionModule();
        initializeVAD();
        addSystemEventListener();
        initializeUI();
        fixVoiceButtonForProduction();
    }

    function initStorages() {
        loadKnowledgeData();
    }

    function initializeDHVariables() {
        console.log("数字人变量初始化");
        // 变量初始化逻辑
        canvas = els.canvas;
        // gl = canvas.getContext('2d');
        videoElement = els.videoElement;
        messageText = els.messageInput.value;
        // messageInterrupt = els.interruptToggle.checked;
        // messageType = els.messageTypeSelect.value;
        useBehindUi = els.uiAutoHideSwitch.checked;
        useBehindUiTime = parseInt(els.uiAutoHideDelay.value);
        tolerance = parseInt(els.toleranceSlider.value);
        chatModalResetStyle = els.chatModal.style.cssText;
        // 从els.mediaTechPositionMode这个select的多个option中为 selected 的 option 的值（<option value="0" selected>）
        techMediaPositionMode = parseInt(els.mediaTechPositionModeSelect.querySelector('option[selected]').value);
        chatModalSize = {
            width: els.chatModal.style.width,
            height: els.chatModal.style.height
        };
        chatModalOffset = {
            left: els.chatModal.style.left || window.innerWidth / 2 - chatModalSize.width / 2,
            top: els.chatModal.style.top || window.innerHeight / 2 - chatModalSize.height / 2
        };
    }

    // 数字人基础板块控件事件绑定方法
    function addSystemEventListener() {
        
        // 监听鼠标移动事件，显示UI并重置计时器
        document.addEventListener('mousemove', function() {
            showUIElements();
        });
        // 监听触摸事件，显示UI并重置计时器
        document.addEventListener('touchstart', function() {
            showUIElements();
        });
        
        // 数字人控制面板标签页切换
        document.querySelectorAll('.tabs').forEach(tabGroup => {
            const modalType = tabGroup.dataset.modal;
            
            tabGroup.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.dataset.tab;
                    switchTab(modalType, targetTab, button);
                });
            });
        });

        
        els.messageInput.addEventListener('input', function(e) {
            messageText = this.value;
            // console.log("messageText: ", messageText);
        });

        // els.interruptToggle.addEventListener('change', function() {
        //     messageInterrupt = this.checked;

        //     // 消息打断切换日志打印
        //     if (messageInterrupt) {
        //         console.log("数字人对话打断已设置");
        //     } else {
        //         console.log("数字人对话打断已取消");
        //     }
        // });

        // els.messageTypeSelect.addEventListener('change', function() {
        //     console.log("消息类型已切换为:", this.value);
        //     messageType = this.value;
        // })

        els.configSelect.addEventListener('input', function() {
            // console.log("人设控制输入：", this.value);
            personaText = this.value;
        });

        els.submitConfigButton.addEventListener('click', function() {
            console.log("提交人设配置");
            // 提交人设配置
            setConfig();
        });
        
        // 添加点击事件使页面获得焦点
        document.addEventListener('click', function() {
            if (document.activeElement.tagName !== 'INPUT' && 
                document.activeElement.tagName !== 'TEXTAREA' &&
                document.activeElement.tagName !== 'SELECT') {
                document.body.focus();
            }
        });
        // 页面加载时设置焦点
        window.addEventListener('load', function() {
            document.body.tabIndex = -1;
            document.body.focus();
            console.log('页面已获得焦点');
        });
        document.addEventListener('keydown', function(e) {
            console.log('键盘按下事件触发:', e.code); // 添加调试信息
            if (e.code === 'Space') {
                if (isAltPressed) {
                    // 如果Alt键已经激活，则不激活空格键功能
                    return;
                }
                if (isSpacePressed) {
                    return;
                }
                isSpacePressed = true;
                console.log('空格键激活');
                els.canvas.classList.add('draggable');
                els.windowStatusElement.textContent = '当前模式：数字人调整 - 鼠标可拖动数字人位置和滚轮缩放';
                els.windowStatusElement.style.color = 'var(--primary-color)';
            } 
            if (e.key === 'Alt') {
                if (isSpacePressed) {
                    // 如果空格键已经激活，则不激活Alt键功能
                    return;
                }
                if (isAltPressed) {
                    return;
                }
                isAltPressed = true; 
                console.log('Alt键激活');
                els.windowStatusElement.textContent = '当前模式：背景调整 - 鼠标  可拖动背景位置和滚轮缩放';
                els.windowStatusElement.style.color = 'var(--secondary-color)';
                // 阻止浏览器默认行为（如菜单显示）
                e.preventDefault();
            }
        });
        document.addEventListener('keyup', function(e) {
            console.log('键盘释放事件触发:', e.code); // 添加调试信息
            if (e.code === 'Space') {
                isSpacePressed = false;
                els.canvas.classList.remove('draggable');
                els.windowStatusElement.textContent = '按住「空格键」可调整数字人，按「Alt」键可调整背景';
                els.windowStatusElement.style.color = 'var(--text-secondary)';
            }
            if (e.key === 'Alt') {
                isAltPressed = false;
                isDraggingBg = false;
                els.windowStatusElement.textContent = '按住「空格键」可调整数字人，按「Alt」键可调整背景';
                els.windowStatusElement.style.color = 'var(--text-secondary)';
            }
        });

        // 设置模态框的显示逻辑 
        // 添加设置模态框展开/收起功能
        // 添加设置模态框展开/收起功能
        els.settingsModalToggle.addEventListener('click', function() {
            const settingsModal = els.settingsModal;
            if (settingsModal) {
                // 如果当前是收起状态，先显示元素，然后再添加expanded类
                if (!settingsModal.classList.contains('expanded')) {
                    // 先设置display为block
                    settingsModal.style.display = 'block';
                    
                    // 强制浏览器重排，确保过渡动画生效
                    void settingsModal.offsetHeight;
                    
                    // 然后添加expanded类
                    requestAnimationFrame(() => {
                        settingsModal.classList.add('expanded');
                        this.classList.add('expanded');
                    });
                    
                    // 延迟确保位置在视口内
                    setTimeout(() => windowResizeHandler(settingsModal, 0), 50);
                } else {
                    // 移除expanded类，触发收起动画
                    settingsModal.classList.remove('expanded');
                    this.classList.remove('expanded');
                    
                    // 延迟隐藏，等待关闭动画完成
                    setTimeout(() => {
                        if (!settingsModal.classList.contains('expanded')) {
                            settingsModal.style.display = 'none';
                        }
                    }, 300);
                }
            }
        });
        
        // 修改设置按钮点击事件，适应新的模态框样式
        els.settingsButton.addEventListener('click', function() {
            const settingsModal = els.settingsModal;
            if (settingsModal) {
                // 如果当前是收起状态，先显示元素，然后再添加expanded类
                if (!settingsModal.classList.contains('expanded')) {
                    // 先设置display为block
                    settingsModal.style.display = 'block';
                    
                    // 强制浏览器重排，确保过渡动画生效
                    void settingsModal.offsetHeight;
                    
                    // 然后添加expanded类
                    requestAnimationFrame(() => {
                        settingsModal.classList.add('expanded');
                        this.classList.add('expanded');
                    });
                    
                    // 延迟确保位置在视口内
                    setTimeout(() => windowResizeHandler(settingsModal, 0), 50);
                } else {
                    // 移除expanded类，触发收起动画
                    settingsModal.classList.remove('expanded');
                    this.classList.remove('expanded');
                    
                    // 延迟隐藏，等待关闭动画完成
                    setTimeout(() => {
                        if (!settingsModal.classList.contains('expanded')) {
                            settingsModal.style.display = 'none';
                        }
                    }, 300);
                }
            }
        });
        
        // 修改关闭按钮事件，适应新的模态框样式
        els.closeModalButton.addEventListener('click', function() {
            const settingsModal = els.settingsModal;
            const settingsModalToggle = els.settingsModalToggle;
            if (settingsModal && settingsModalToggle) {
                settingsModal.classList.remove('expanded');
                settingsModalToggle.classList.remove('expanded');
                // 延迟隐藏，等待关闭动画完成
                setTimeout(() => {
                    if (!settingsModal.classList.contains('expanded')) {
                        settingsModal.style.display = 'none';
                    }
                }, 300);
            }
        });
        
        // 设置模态框的头的拖动封装逻辑
        // addDraggingEventHandle(els.settingsModal, els.dragHandle, settingsModalOffset, true, 'draggable', 'dragging');

        addDraggingEventHandle(els.canvas, els.canvas, dhOffset, false, 'draggable', 'dragging', () => isSpacePressed, dhOffsetElements);
        
        addDraggingEventHandle(els.chatModal, els.chatModalHeader, chatModalOffset, true, 'draggable', 'dragging');
        
        // 测试模态框
        addDraggingEventHandle(els.testModal, els.testModalHeader, testModalOffset, true, 'draggable', 'dragging');
        // 对话模态框
        addResizeEventHandle(els.chatModal, chatModalOffset, chatModalSize, true, true, null, 20, [els.chatModalHeader]);
        // 设置模态框
        addResizeEventHandle(els.settingsModal, settingsModalOffset, settingsModalSize, true, true, null, 20, [els.settingsModalHeader]);

        // 对话框相关事件
        els.chatPosLeftCenter.addEventListener('click', function() {
            // 显式传入动画速度0.1，这样会执行动画过渡
            positionModeElement(els.chatModal, chatModalOffset, chatModalOffsetElements, 'left-center', 30, 30, 1.0);
        });
        els.chatPosCenterBottom.addEventListener('click', function() {
            positionModeElement(els.chatModal, chatModalOffset, chatModalOffsetElements, 'center-bottom', 0, 0, 1.0);
        });
        els.chatPosRightCenter.addEventListener('click', function() {
            positionModeElement(els.chatModal, chatModalOffset, chatModalOffsetElements, 'right-center', 30, 30, 1.0);
        });
        els.chatPosLeftBottom.addEventListener('click', function() {
            positionModeElement(els.chatModal, chatModalOffset, chatModalOffsetElements, 'left-bottom', 30, 30, 1.0);
        });
        els.chatPosRightBottom.addEventListener('click', function() {
            positionModeElement(els.chatModal, chatModalOffset, chatModalOffsetElements, 'right-bottom', 30, 30, 1.0);
        });
        els.chatXPositionSlider.addEventListener('input', function() {
            chatModalOffset.x = updateSliderAndInput(this, els.chatXPositionValue, this.value);
            // 更新chatModal的left值
            els.chatModal.style.left = chatModalOffset.x + 'px';
        });
        els.chatYPositionSlider.addEventListener('input', function() {
            chatModalOffset.y = updateSliderAndInput(this, els.chatYPositionValue, this.value);
            // 更新chatModal的top值
            els.chatModal.style.top = chatModalOffset.y + 'px';
        });
        els.chatWidthSlider.addEventListener('input', function() {
            chatModalSize.width = updateSliderAndInput(this, els.chatWidthValue, this.value);
            // 更新chatModal的width值
            els.chatModal.style.width = chatModalSize.width + 'px';
        });
        els.chatHeightSlider.addEventListener('input', function() {
            chatModalSize.height = updateSliderAndInput(this, els.chatHeightValue, this.value);
            // 更新chatModal的height值
            els.chatModal.style.height = chatModalSize.height + 'px';
        });

        // 添加滚轮事件监听，并拿到清除方法
        const clearWheelEvent = document.addEventListener('wheel', (e) => eventDHWheelHandle(e, els.canvas, 0.1, isSpacePressed));

        // 添加对性能监控开关的事件监听
        els.showPerformanceStats.addEventListener('change', function() {
            // 使用classList来添加/移除.visible类
            if (this.checked) {
                els.performanceStats.classList.add('visible');
            } else {
                els.performanceStats.classList.remove('visible');
            }
            
            // 更新开关状态显示文本
            const switchValueElement = this.parentElement.nextElementSibling;
            if (switchValueElement && switchValueElement.classList.contains('switch-value')) {
                switchValueElement.textContent = this.checked ? '开启' : '关闭';
            }
        });

        // 添加最大分辨率滑块事件监听
        // els.maxResolutionSlider.addEventListener('input', function() {
        //     const newValue = parseInt(this.value);
        //     maxCanvasResolution = newValue;
        //     els.maxResolutionValue.value = newValue;
        //     console.log("Canvas最大分辨率已设置为:", maxCanvasResolution);
        // });
        
        // els.maxResolutionValue.addEventListener('change', function() {
        //     const newValue = parseInt(this.value);
        //     if (!isNaN(newValue) && newValue > 0) {
        //         maxCanvasResolution = newValue;
        //         els.maxResolutionSlider.value = newValue;
        //         console.log("Canvas最大分辨率已设置为:", maxCanvasResolution);
        //     }
        // });

        els.xOffsetSlider.addEventListener('input', function() {
            els.canvas.style.left = this.value + 'px';
            dhOffset.x = updateSliderAndInput(this, els.xOffsetValue, this.value);

        });
        els.xOffsetValue.addEventListener('input', function() {
            els.canvas.style.left = this.value + 'px';
            dhOffset.x = updateSliderAndInput(els.xOffsetSlider, this, this.value);
        });


        els.yOffsetSlider.addEventListener('input', function() {
            els.canvas.style.top = this.value + 'px';
            dhOffset.y = updateSliderAndInput(this, els.yOffsetValue, this.value);
        });
        els.yOffsetValue.addEventListener('input', function() {
            els.canvas.style.top = this.value + 'px';
            dhOffset.y = updateSliderAndInput(els.yOffsetSlider, this, this.value);
        });


        els.positionLeftBottom.addEventListener('click', function() {
            // 显式传入动画速度0.1，这样会执行动画过渡
            // setupCanvasSize(400, 720, true, true, true, 1000, 'center', 0.1);
            // positionAndResizeDigitalHuman('left-top', 500, 900, 0, 0, 0.1);
            // 通过 positionAndResizeElement 返回值获取动画前的位置
            // dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, 'left-bottom', dhOffset, dhSize, dhOffsetAndSizeElements, 0, 0, 0.1, null, 1, 1);
            dhPreviousSizeAndPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'left-bottom', 0, 0, 0.1);
            console.log("左下定位的 dhPreviousSizeAndPosition:", dhPreviousSizeAndPosition);
            // 获取动画后的位置
        });
        els.positionCenterMiddle.addEventListener('click', function() {
            // positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'center', 0, 0, 0.1);
            // const targetOffset = {
            //     x: dhPreviousSizeAndPosition.previousLeft,
            //     y: dhPreviousSizeAndPosition.previousTop,
            // };
            // const targetSize = {
            //     width: dhPreviousSizeAndPosition.previousWidth,
            //     height: dhPreviousSizeAndPosition.previousHeight,
            // };
            // dhPreviousSizeAndPosition = positionAndResizeElement(els.canvas, null, dhOffset, dhSize, dhOffsetAndSizeElements, 0, 0, 0.1, null, targetSize.width, targetSize.height, targetOffset.x, targetOffset.y);
            dhPreviousSizeAndPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'center', 0, 0, 0.1);
            console.log("中心中部定位的 dhPreviousSizeAndPosition:", dhPreviousSizeAndPosition);
        });
        els.positionCenterBottom.addEventListener('click', function() {
            // const precisionPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'center-bottom', 0, 0, 0.1);
            // dhPreviousSizeAndPosition = resetDigitalHuman();
            dhPreviousSizeAndPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'center-bottom', 0, 0, 0.1);
            console.log("中心底部定位的 dhPreviousSizeAndPosition:", dhPreviousSizeAndPosition);
        });
        els.positionRightBottom.addEventListener('click', function() {
            dhPreviousSizeAndPosition = positionModeElement(els.canvas, dhOffset, dhOffsetElements, 'right-bottom', 0, 0, 0.1);
            console.log("右下定位的 dhPreviousSizeAndPosition:", dhPreviousSizeAndPosition);
        });


        els.bgColorPicker.addEventListener('input', function() {
            inputBgColorHex = this.value;
            bgColorRGB = hexToRgb(inputBgColorHex);
            els.bgColorValue.value = this.value;
        });
        els.bgColorValue.addEventListener('input', function() {
            inputBgColorHex = this.value;
            bgColorRGB = hexToRgb(inputBgColorHex);
            els.bgColorPicker.value = this.value;
        });
        els.bgColorPickerButton.addEventListener('click', function() {
            // 调用api接口，获取颜色
            // 创建临时Canvas来分析视频帧
            const tempCanvas = document.createElement('canvas');
            const tempCtx = tempCanvas.getContext('2d');
            
            // 在临时Canvas上绘制第一帧，renderWidth改为数字人宽度，renderHeight改为数字人高度
            tempCtx.drawImage(videoElement, 0, 0, dhSize.width, dhSize.height);
            
            // 使用优化的背景颜色检测方法
            bgColorRGB = detectDominantColor(tempCtx, dhSize.width, dhSize.height);
            console.log("一键抠图bgColorRGB", bgColorRGB);
            // 将bgColorRGB转换为十六进制
            inputBgColorHex = rgbToHex(bgColorRGB.r, bgColorRGB.g, bgColorRGB.b);
            tolerance = updateSliderAndInput(els.toleranceSlider, els.toleranceValue, 50);
            
            // 更新背景颜色选择器
            els.bgColorPicker.value = inputBgColorHex;
            // 更新背景颜色值
            els.bgColorValue.value = inputBgColorHex;
            showToast(`一键抠图成功，背景颜色为：${inputBgColorHex}`, "success");
        });


        els.toleranceSlider.addEventListener('input', function() {
            tolerance = updateSliderAndInput(this, els.toleranceValue, this.value);
        });
        els.toleranceValue.addEventListener('input', function() {
            tolerance = updateSliderAndInput(els.toleranceSlider, this, this.value);
        });


        els.antialiasingSlider.addEventListener('input', function() {
            antiAliasingStrength = updateSliderAndInput(this, els.antialiasingValue, this.value);
        });
        els.antialiasingValue.addEventListener('input', function() {
            antiAliasingStrength = updateSliderAndInput(els.antialiasingSlider, this, this.value);
        });


        els.originRatioLockCheckbox.addEventListener('change', function() {
            isRatioLocked = this.checked;
            if (isRatioLocked) {
                // 取消ratioLockCheckbox的开关
                els.ratioLockCheckbox.checked = false;
                customAspectRatio = aspectRatio;
                const oldHeight = dhSize.height;
                dhSize.height = Math.round(dhSize.width / customAspectRatio);
                
                // 更新UI控件
                updateSliderAndInput(els.customHeightSlider, els.customHeightValue, dhSize.height);
                
                // 使用setupCanvasSize更新尺寸，保持中心点不变，添加轻微动画效果
                if (oldHeight !== dhSize.height) {
                    setupCanvasSize(
                        dhSize.width,  // 确保是数字
                        dhSize.height, // 确保是数字
                        true,                   // 强制更新
                        true,                   // 限制最大尺寸
                        maxCanvasResolution,    // 最大高度限制
                        'center',               // 锚点: 中心点
                        null                    // 轻微动画效果
                    );
                }
            }
        });

        els.ratioLockCheckbox.addEventListener('change', function() {
            isRatioLocked = this.checked;
            if (isRatioLocked) {
                els.originRatioLockCheckbox.checked = false;
                console.log("锁定比例");
                console.log("customWidth", dhSize.width);
                console.log("customHeight", dhSize.height);
                customAspectRatio = dhSize.width / dhSize.height;
                
                // 立即应用当前比例
                updateSliderAndInput(els.customHeightSlider, els.customHeightValue, dhSize.height);
            }
        });
         
        els.customWidthSlider.addEventListener('input', function() {
            // const oldWidth = customWidth;
            dhSize.width = updateSliderAndInput(this, els.customWidthValue, this.value);
            if (isRatioLocked) {
                dhSize.height = Math.round(dhSize.width / customAspectRatio);
                updateSliderAndInput(els.customHeightSlider, els.customHeightValue, dhSize.height);
            }
            // 使用setupCanvasSize更新尺寸，保持中心点不变，添加轻微动画效果
            setupCanvasSize(dhSize.width, dhSize.height, true, true, maxCanvasResolution, 'center', null);
        });

        els.customWidthValue.addEventListener('input', function() {
            // const oldWidth = customWidth;
            dhSize.width = updateSliderAndInput(els.customWidthSlider, this, this.value);
            if (isRatioLocked) {
                dhSize.height = Math.round(dhSize.width / customAspectRatio);
                updateSliderAndInput(els.customHeightSlider, els.customHeightValue, dhSize.height);
            }
            // 使用setupCanvasSize更新尺寸，保持中心点不变，添加轻微动画效果
            setupCanvasSize(dhSize.width, dhSize.height, true, true, maxCanvasResolution, 'center', null);
        });

        els.customHeightSlider.addEventListener('input', function() {
            // const oldHeight = customHeight;
            dhSize.height = updateSliderAndInput(this, els.customHeightValue, this.value);
            if (isRatioLocked) {
                dhSize.width = Math.round(dhSize.height * customAspectRatio);
                updateSliderAndInput(els.customWidthSlider, els.customWidthValue, dhSize.width);
            }
            // 使用setupCanvasSize更新尺寸，保持中心点不变，添加轻微动画效果
            setupCanvasSize(dhSize.width, dhSize.height, true, true, maxCanvasResolution, 'center', null);
        });

        els.customHeightValue.addEventListener('input', function() {
            // const oldHeight = customHeight;
            dhSize.height = updateSliderAndInput(els.customHeightSlider, this, this.value);
            if (isRatioLocked) {
                dhSize.width = Math.round(dhSize.height * customAspectRatio);
                updateSliderAndInput(els.customWidthSlider, els.customWidthValue, dhSize.width);
            }
            // 使用setupCanvasSize更新尺寸，保持中心点不变，添加轻微动画效果
            setupCanvasSize(dhSize.width, dhSize.height, true, true, maxCanvasResolution, 'center', null);
        });

        // UI 隐藏功能绑定
        els.uiAutoHideSwitch.addEventListener('change', function() {
            useBehindUi = this.checked;
            console.log("useBehindUi", useBehindUi);
            
            // 立即应用新设置
            if (window.uiAutoHideController) {
                if (useBehindUi) {
                    // 如果启用自动隐藏，先显示UI，然后启动新的计时器
                    showUIElements();
                } else {
                    // 如果禁用自动隐藏，确保UI是可见的
                    showUIElements();
                }
            }
        });

        els.uiAutoHideDelay.addEventListener('input', function() {
            useBehindUiTime = updateSliderAndInput(this, els.uiAutoHideDelayValue, this.value);
            if (useBehindUi) {
                showUIElements();
            }
        });

        els.uiAutoHideDelayValue.addEventListener('input', function() {
            useBehindUiTime = updateSliderAndInput(els.uiAutoHideDelay, this, this.value);
            if (useBehindUi) {
                showUIElements();
            }
        });

        // 初始化背景上传按钮
        els.backgroundUpload.addEventListener('change', handleBackgroundUpload);

        // 初始化预设背景项目点击事件
        els.bgPresetItems.forEach(item => {
            item.addEventListener('click', function() {
                const type = this.dataset.type;
                const src = this.dataset.src;
                const position = bgFit ? bgFit : this.dataset.position;
                console.log("预设背景项目点击事件触发", type, src, position);
                if (type && src) {
                    // 确保明确地传递 position 参数
                    if (position) {
                        setBackgroundMedia(type, src, true, true, true, position, true, 500);
                    } else {
                        setBackgroundMedia(type, src, true, true, true, 'cover', true, 500);
                    }
                }
            });
        });

        els.showChatModalButton.addEventListener('click', function() {
            if (els.chatModal.classList.contains('show')) {
                els.chatModal.classList.remove('show');
            } else {
                els.chatModal.classList.add('show');
            }
        });

        els.chatImages.forEach((image) => {
            image.addEventListener('click', (event) => {
              const imageSrc = event.target.src;
              els.imagePreview.src = imageSrc;
              els.imagePreviewModal.style.display = 'flex';
            });
        });
          
          // 点击遮罩层关闭预览
        els.imagePreviewModal.addEventListener('click', () => {
            els.imagePreviewModal.style.display = 'none';
        });

        els.testButton.addEventListener('click', function() {
            // 和settings-button一样，打开一个modal
            if (els.testModal.classList.contains('show')) {
                els.testModal.classList.remove('show');
            } else {
                els.testModal.classList.add('show');
            }
        });

        els.closeTestModalButton.addEventListener('click', function() {
            if (els.testModal.classList.contains('show')) {
                els.testModal.classList.remove('show');
            }
        });
        
        els.closeTestModalButton.addEventListener('touchend', function(e) {
            e.preventDefault();
            els.testModal.classList.remove('show');
        });
        
        // 纯 ui 逻辑部分结束
          

        // 启动按钮
        els.useCanvas2D.addEventListener('change', function() {
            useCanvas2D = this.checked;
        });
        
        els.startButton.addEventListener('click', start);
        // 停止按钮
        els.stopButton.addEventListener('click', stop);
        // 连接WebSocket按钮
        // els.connectWSButton.addEventListener('click', async () => {
        //     try {
        //         await connectOCServer();
        //         els.connectWSButton.style.display = 'none';
        //         els.disconWsButton.style.display = 'block';
        //         showToast('OC服务器连接成功');
        // } catch (error) {
        //         console.error('连接OC服务器失败:', error);
        //         showToast('连接OC服务器失败: ' + error.message, 'error');
        //     }
        // });

        // 断开远控链接按钮
        // els.disconWsButton.addEventListener('click', () => {
        //     if (ocConnection) {
        //         ocConnection.disconnect('用户手动断开连接');
        //         ocConnection = null;
        //         els.connectWSButton.style.display = 'block';
        //         els.disconWsButton.style.display = 'none';
        //         showToast('已断开OC服务器连接');
        //     } else {
        //         showToast('未连接到OC服务器');
        //     }
        // });

        // 聊天表单提交处理
        // const echoForm = document.getElementById('echo-form');
        // const sendButton = echoForm ? echoForm.querySelector('button[type="button"]') : null;
        // 为聊天框输入人添加输入监听，将输入文本存入变量
        els.closeChatModalButton.addEventListener('click', function() {
            // 如果聊天框是显示的，则关闭
            if (els.chatModal.classList.contains('show')) {
                console.log("关闭聊天框");
                els.chatModal.classList.remove('show');
            }
        });

        // 手机端关闭按钮
        els.closeChatModalButton.addEventListener('touchend', function() {
            // 如果聊天框是显示的，则关闭
            if (els.chatModal.classList.contains('show')) {
                console.log("关闭聊天框");
                els.chatModal.classList.remove('show');
            }
        });

        // 同时为表单和发送按钮添加事件处理
        // 保留表单提交事件处理，以便回车键可以提交
        // els.echoForm.addEventListener('submit', async function(e) {
        //     e.preventDefault(); // 阻止默认提交行为
        //     clearInSettingMessageInput();
        //     const res = await handleMessageSend(messageText);
        //     messageText = null;
        //     // if (res && res.code === 0) {
        //     //     messageText = null;
        //     //     clearInSettingMessageInput();
        //     // }
        // });

        // 为发送按钮添加点击事件
        // els.sendMessageButtonInSettingsModal.addEventListener('click', async function(e) {
        //     e.preventDefault();
        //     clearInSettingMessageInput();
        //     const res = await handleMessageSend(messageText);
        //     messageText = null;
        //     // if (res && res.code === 0) {
        //     //     messageText = null;
        //     //     clearInSettingMessageInput();
        //     // }
        // });

        els.chatInput.addEventListener('input', function(e) {
            chatInputValue = this.value;
        });

        els.chatInput.addEventListener('keydown', async function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                clearInChatMessageInput();
                const res = await handleMessageSend(chatInputValue);
                chatInputValue = null;
                // if (res && res.code === 0) {
                //     chatInputValue = null;
                //     clearInChatMessageInput();
                // }
            }
        });

        els.sendMessageButtonInChatModal.addEventListener('click', async function(e) {
            e.preventDefault();
            clearInChatMessageInput();
            const res = await handleMessageSend(chatInputValue);
            chatInputValue = null;
            if (res && res.code === 0) {
                // chatInputValue = null;
                // clearInChatMessageInput();
                showToast('发送成功', 'success');
            } else {
                showToast('发送失败' + (res && res.message || ''), 'error');
            }
        });

        // // 无人直播路径提交按钮
        // els.submitNbPath.addEventListener('click', function(e) {
        //     e.preventDefault();
        //     submitNbLivePath();
        // });

        // // 无人直播路径输入框
        // els.nbLivePathInput.addEventListener('input', function() {
        //     nbLivePath = this.value;
        // });


        // 主题控制
        els.themeButtonsBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                applyTheme(this.dataset.theme);
            });
        });
        
         // 字号控制
        els.fontSizeSlider.addEventListener('input', function() {
            fontSize = updateSliderAndInput(this, els.fontSizeValue, this.value);
            els.chatContainer.style.fontSize = fontSize + 'px';
            // els.chatInput.style.fontSize = fontSize + 'px';
            
            // 根据字号调整容器大小
            // const scale = fontSize / 14;
            // const newWidth = Math.max(350, 400 * scale);
            // // 让最大高度等于屏幕减去40px
            // const newHeight = Math.max(500, 600 * scale);
            // els.chatModal.style.width = newWidth + 'px';
            // els.chatModal.style.height = newHeight + 'px';
        });

        els.fontSizeValue.addEventListener('input', function() {
            fontSize = updateSliderAndInput(els.fontSizeSlider, this, this.value);
            els.chatModal.style.fontSize = fontSize + 'px';
            
            // 根据字号调整容器大小
            const scale = fontSize / 14;
            const newWidth = Math.max(350, 400 * scale);
            // 让最大高度等于屏幕减去40px
            const newHeight = Math.max(500, 600 * scale);
            els.chatModal.style.width = newWidth + 'px';
            els.chatModal.style.height = newHeight + 'px';
        });


        // 字体控制
        els.fontFamilySelect.addEventListener('change', function() {
            els.chatModal.style.fontFamily = this.value;
        });

        let clearChatModalDraggingEventHandle = null;
        
        els.hideHeaderCheckbox.addEventListener('change', function() {
            // 改为添加类
            if (this.checked) {
                els.chatModalheader.classList.add('hide');
                clearChatModalDraggingEventHandle = addDraggingEventHandle(els.chatModal, els.chatContainer, chatModalOffset, true, 'draggable', 'dragging');
            } else {
                els.chatModalheader.classList.remove('hide');
                clearChatModalDraggingEventHandle();
            }
        });

        els.hideChatAreaCheckbox.addEventListener('change', function() {
            // 改为添加类
            if (this.checked) {
                els.chatInputArea.classList.add('hide');
            } else {
                els.chatInputArea.classList.remove('hide');
            }
        });

        // 透明度控制
        els.modalOpacitySlider.addEventListener('input', function() {
            chatModalOpacity = updateSliderAndInput(this, els.modalOpacityValue, this.value);
            
            // 更新对话框背景透明度，使用保存的RGB值
            els.chatModal.style.backgroundColor = `rgba(${originalModalRGB}, ${chatModalOpacity})`;
            els.chatModalheader.style.backgroundColor = `rgba(${originalHeaderRGB}, ${chatModalOpacity})`;
            els.chatInputArea.style.backgroundColor = `rgba(${originalInputAreaRGB}, ${chatModalOpacity})`;
            els.sendMessageButtonInChatModal.style.backgroundColor = `rgba(${originalChatSendButtonRGB})`;

            // 同步更新容器透明度控件
            containerOpacity = chatModalOpacity;
            updateSliderAndInput(els.containerOpacitySlider, els.containerOpacityValue, chatModalOpacity);
            
            // 更新容器背景透明度，使用保存的RGB值
            els.chatContainer.style.color = `rgba(${originalContainerRGB}, ${containerOpacity})`;
        });
        
        els.modalOpacityValue.addEventListener('input', function() {
            chatModalOpacity = updateSliderAndInput(els.modalOpacitySlider, this, this.value);
            
            // 更新对话框背景透明度，使用保存的RGB值
            els.chatModal.style.backgroundColor = `rgba(${originalModalRGB}, ${chatModalOpacity})`;
            els.chatModalheader.style.backgroundColor = `rgba(${originalHeaderRGB}, ${chatModalOpacity})`;

            // 同步更新容器透明度控件
            containerOpacity = chatModalOpacity;
            updateSliderAndInput(els.containerOpacitySlider, els.containerOpacityValue, chatModalOpacity);
            
            // 更新容器背景透明度，使用保存的RGB值
            els.chatContainer.style.color = `rgba(${originalContainerRGB}, ${containerOpacity})`;
        });
        
        // els.containerOpacitySlider.addEventListener('input', function() {
        //     containerOpacity = updateSliderAndInput(this, els.containerOpacityValue, this.value);
            
        //     // 更新容器背景透明度，使用保存的RGB值
        //     els.chatContainer.style.color = `rgba(${originalContainerRGB}, ${containerOpacity})`;

        //     // 同步更新对话框透明度控件
        //     chatModalOpacity = containerOpacity;
            
        //     // 更新对话框背景透明度，使用保存的RGB值
        //     els.chatModal.style.backgroundColor = `rgba(${originalModalRGB}, ${chatModalOpacity})`;
        //     els.chatModalheader.style.backgroundColor = `rgba(${originalHeaderRGB}, ${chatModalOpacity})`;
        // });
        
        // els.containerOpacityValue.addEventListener('input', function() {
        //     containerOpacity = updateSliderAndInput(els.containerOpacitySlider, this, this.value);
            
        //     // 更新容器背景透明度，使用保存的RGB值
        //     els.chatContainer.style.color = `rgba(${originalContainerRGB}, ${containerOpacity})`;

        //     // 同步更新对话框透明度控件
        //     chatModalOpacity = containerOpacity;
            
        //     // 更新对话框背景透明度，使用保存的RGB值
        //     els.chatModal.style.backgroundColor = `rgba(${originalModalRGB}, ${chatModalOpacity})`;
        //     els.chatModalheader.style.backgroundColor = `rgba(${originalHeaderRGB}, ${chatModalOpacity})`;
        // });

        els.messageOpacitySlider.addEventListener('input', function() {
            messageOpacity = updateSliderAndInput(this, els.messageOpacityValue, this.value);
            
            // 更新系统消息透明度，使用保存的RGB值
            const systemMessages = document.querySelectorAll('.chat-item:not(.user) .chat-message');
            systemMessages.forEach(msg => {
                msg.style.backgroundColor = `rgba(${originalSystemMessageRGB}, ${messageOpacity})`;
            });
            
            // 更新用户消息透明度，使用保存的RGB值
            const userMessages = document.querySelectorAll('.chat-item.user .chat-message');
            userMessages.forEach(msg => {
                msg.style.backgroundColor = `rgba(${originalUserMessageRGB}, ${messageOpacity})`;
            });
        });

        els.messageOpacityValue.addEventListener('input', function() {
            messageOpacity = updateSliderAndInput(els.messageOpacitySlider, this, this.value);
            
            // 更新系统消息透明度，使用保存的RGB值
            const systemMessages = document.querySelectorAll('.chat-item:not(.user) .chat-message');
            systemMessages.forEach(msg => {
                msg.style.backgroundColor = `rgba(${originalSystemMessageRGB}, ${messageOpacity})`;
            });
            
            // 更新用户消息透明度，使用保存的RGB值
            const userMessages = document.querySelectorAll('.chat-item.user .chat-message');
            userMessages.forEach(msg => {
                msg.style.backgroundColor = `rgba(${originalUserMessageRGB}, ${messageOpacity})`;
            });
        });

        // 颜色选择器
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('color-btn')) {
                const color = e.target.dataset.color;
                const target = e.target.dataset.target;

                if (target === 'modal-bg') {
                    els.chatModal.style.backgroundColor = color;
                    // 同时更新header背景，保持相同的透明度
                    const header = els.chatModal.querySelector('.chat-modal-header');
                    const colorMatch = color.match(/rgba?\(([^)]+)\)/);
                    if (colorMatch) {
                        const opacity = els.modalOpacitySlider.value;
                        const rgb = colorMatch[1].split(',').slice(0, 3).join(',');
                        // header使用明显更深的颜色
                        const headerRgb = rgb.split(',').map((val, i) => {
                            // 将每个RGB分量减少60，创造更强的对比度
                            if (i < 3) return Math.max(0, parseInt(val.trim()) - 60);
                            return val;
                        }).join(',');
                        els.chatModalheader.style.backgroundColor = `rgba(${headerRgb}, ${opacity})`;
                    }
                } else if (target === 'user-bg') {
                    const userMessages = document.querySelectorAll('.chat-item.user .chat-message');
                    userMessages.forEach(msg => msg.style.backgroundColor = color);
                } else if (target === 'user-color') {
                    const userMessages = document.querySelectorAll('.chat-item.user .chat-message');
                    userMessages.forEach(msg => msg.style.color = color);
                } else if (target === 'system-bg') {
                    const systemMessages = document.querySelectorAll('.chat-item:not(.user) .chat-message');
                    systemMessages.forEach(msg => msg.style.backgroundColor = color);
                } else if (target === 'system-color') {
                    const systemMessages = document.querySelectorAll('.chat-item:not(.user) .chat-message');
                    systemMessages.forEach(msg => msg.style.color = color);
                } else {
                    // 整体文本颜色 - 只影响聊天内容区域
                    els.chatContainer.style.color = color;
                    // 同时更新所有没有自定义颜色的消息
                    const allMessages = document.querySelectorAll('.chat-message');
                    allMessages.forEach(msg => {
                        if (!msg.style.color) {
                            msg.style.color = color;
                        }
                    });
                }
            }
        });

        // 媒体框


        // --- 文件点击上传相关 ---
        // 检查 els.fileInput 是否存在，并且之前没有绑定过 'change' 事件监听器
        if (els.fileInput && !els.fileInput._changeEventListenerAttached) {
            els.fileInput.addEventListener('change', handleFileSelect);
            els.fileInput._changeEventListenerAttached = true; // 标记已绑定 'change' 事件
            console.log("文件输入框 'change' 事件监听器已附加到 els.fileInput。");
        }

        // --- 媒体框基本控制 ---
        if (els.toggleMediaFrame && !els.toggleMediaFrame._clickEventListenerAttached) {
            els.toggleMediaFrame.addEventListener('click', () => {
                openMediaFrameAndPositionDH();
            });
            els.toggleMediaFrame._clickEventListenerAttached = true;
        }

        if (els.mediaFrameCloseButton && !els.mediaFrameCloseButton._clickEventListenerAttached) {
            els.mediaFrameCloseButton.addEventListener('click', () => {
                closeClearMeFAndDHReset();                
            });
            els.mediaFrameCloseButton._clickEventListenerAttached = true;
        }

        if (els.mediaTechPositionModeSelect) {
            // 遍历其下的option为每个option添加change事件
            els.mediaTechPositionModeSelect.addEventListener('change', (e) => {
                techMediaPositionMode = parseInt(e.target.value);
                console.log("techMediaPositionMode:", techMediaPositionMode);
            });
            els.mediaTechPositionModeSelect._clickEventListenerAttached = true;
        }

        if (els.closeMediaFrame && !els.closeMediaFrame._clickEventListenerAttached) {
            els.closeMediaFrame.addEventListener('click', () => {
                closeMediaFrame();
            });
            els.closeMediaFrame._clickEventListenerAttached = true;
        }
        
        // 添加媒体移除按钮的点击事件
        if (els.mediaRemoveButton && !els.mediaRemoveButton._clickEventListenerAttached) {
            els.mediaRemoveButton.addEventListener('click', () => {
                // 创建一个清理事件的函数，用于处理音频播放器等需要特殊清理的情况
                resetUploadZone();
            });
            els.mediaRemoveButton._clickEventListenerAttached = true;
        }
        
        // --- 动画按钮 ---
        if (els.animationButtons && typeof els.animationButtons.forEach === 'function' && !els.animationButtons._listenersAttached) {
            // 保存一个按钮列表的强引用，防止垃圾回收
            const buttons = Array.from(els.animationButtons);
            
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const animation = this.dataset.animation;
                    applyAnimation(animation);
                    
                    // 使用保存的buttons引用，而不是重新访问elements.animationButtons
                    buttons.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            if (buttons.length > 0) {
                Object.defineProperty(els.animationButtons, '_listenersAttached', { value: true, writable: false, configurable: true });
            }
        }

        // --- 拖拽区域相关 (包括点击触发文件选择和拖放) ---
        // 检查 els.uploadZone 是否存在，并且之前没有绑定过事件监听器
        if (els.uploadZone && !els.uploadZone._eventListenersAttached) {
            
            addEventUploadZone();

            els.uploadZone._eventListenersAttached = true; // 标记已绑定事件
            console.log("上传区域的事件监听器已附加到 els.uploadZone。");
        }

        // --- ESC 键关闭媒体框 ---
        // 这个监听器可以重复添加，但为了统一性，也加上判断
        if (!document._escKeyListenerForMediaFrameAttached) {
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // 确保只在媒体框相关的元素存在时才调用 closeMediaFrame
                    if (els.mediaFrame && els.mediaFrame.classList.contains('active')) {
                        closeClearMeFAndDHReset();
                    }
                }
            });
            document._escKeyListenerForMediaFrameAttached = true;
        }

        els.resetDhButton.addEventListener('click', () => {
            dhPreviousSizeAndPosition = resetDigitalHuman();
            console.log("dhPreviousSizeAndPosition:", dhPreviousSizeAndPosition);
        });

        // 高级设置
        els.asrToggleAdvanced.addEventListener('click', function() {
            // 切换高级设置区域的显示状态
            if (els.asrAdvancedSettings.style.display === 'none') {
                els.asrAdvancedSettings.style.display = 'block';
                els.asrToggleAdvanced.innerHTML = `
                    隐藏高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom; transform: rotate(180deg); transition: transform 0.3s ease;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
            } else {
                els.asrAdvancedSettings.style.display = 'none';
                els.asrToggleAdvanced.innerHTML = `
                    显示高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom; transition: transform 0.3s ease;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
            }
        });

        // 添加ASR链接按钮
        els.asrBtnConnect.addEventListener('click', async function() {
            try {
                await connectASRServer();
                updateASRStatusHTML('<span style="color:green">ASR服务连接成功，点击开始按钮开始识别</span>');
                els.asrBtnStart.disabled = false;
                els.asrBtnStop.disabled = true;
                els.asrBtnConnect.disabled = true;
            } catch (error) {
                console.error('连接ASR服务器失败:', error);
                updateASRStatusHTML('<span style="color:red">ASR服务连接失败: ' + error.message + '</span>');
            }
        });

        // 添加ASR开始识别按钮
        els.asrBtnStart.addEventListener('click', async function() {
            await startASRRecording();
        });

        // 添加ASR关闭识别按钮
        els.asrBtnStop.addEventListener('click', function() {
            stopASRRecording();
        });

        els.asrWakeupWordBtn.addEventListener('click', function() {
            customWakeupWord = els.asrWakeupWord.value;
            addWakeWord(customWakeupWord, '你好');
            showToast(`已添加唤醒词: ${customWakeupWord}`, 'success');
        });

        // 人脸检测相关事件绑定
        els.faceDetectionToggle.addEventListener('change', function() {
            console.log("faceDetectionToggle 事件触发");
            faceDetectionEnabled = this.checked;
            // if (faceDetectionEnabled && !faceDetectionModuleInitialized) {
            //     initFaceDetectionModule();
            //     faceDetectionModuleInitialized = true;
            // }
            saveFaceDetectionConfig();
            showToast(`人脸检测已${faceDetectionEnabled ? '开启' : '关闭'}`, 
                faceDetectionEnabled ? 'success' : 'info');
        });

        // 人脸验证开关
        els.faceVerificationToggle.addEventListener('change', function() {
            requireFaceVerification = this.checked;
            saveFaceDetectionConfig();
            showToast(`人脸身份验证已${requireFaceVerification ? '开启' : '关闭'}`, 
                requireFaceVerification ? 'success' : 'info');
        });

        // 人脸检测间隔滑块
        els.faceDetectionInterval.addEventListener('input', function() {
            const value = parseInt(this.value);
            els.faceDetectionIntervalValue.value = value;
            faceDetectionInterval = value;
            setDetectionInterval(value);
            saveFaceDetectionConfig();
        });

        // 人脸验证阈值滑块
        els.faceVerificationThreshold.addEventListener('input', function() {
            const value = parseFloat(this.value);
            els.faceVerificationThresholdValue.value = value.toFixed(2);
            faceVerificationThreshold = value;
            setVerificationThreshold(value);
            saveFaceDetectionConfig();
        });

        // 人脸验证超时时间滑块
        els.faceVerificationTimeout.addEventListener('input', function() {
            const value = parseInt(this.value);
            els.faceVerificationTimeoutValue.value = value;
            faceVerificationTimeout = value;
        });
        // 关闭人脸检测框按钮
        els.faceCloseBtn.addEventListener('click', function() {
            // stopFaceDetection();
            closeFaceDetection();
            isFaceVerifying = false;
        });

        // 注册人脸按钮
        els.faceRegisterBtn.addEventListener('click', function() {
            showFaceRegisterDialog();
        });

        // tabs横向滚动
        els.tabsContainer.addEventListener('wheel', function(e) {
            // 阻止默认垂直滚动
            e.preventDefault();
            
            // 将垂直滚动转换为水平滚动
            const scrollAmount = e.deltaY || e.deltaX;
            this.scrollLeft += scrollAmount;
        }, { passive: false });

        els.testModalTabs.addEventListener('wheel', function(e) {
            // 阻止默认垂直滚动
            e.preventDefault();
            
            // 将垂直滚动转换为水平滚动
            const scrollAmount = e.deltaY || e.deltaX;
            this.scrollLeft += scrollAmount;
        }, { passive: false });

        // 唤醒词检测开关
        els.asrWakeupDetectionToggle.addEventListener('change', function() {
            isWakeUp = !this.checked;
            updateWakeupStatusHTML(`<span style="color:#06b6d4">✓ 唤醒词检测已${!isWakeUp ? '开启' : '关闭'}</span>`);
            showToast(`唤醒词检测已${!isWakeUp ? '开启' : '关闭'}`, 
                !isWakeUp ? 'success' : 'info');
        });
    
        els.asrWakeupTimeout.addEventListener('input', function() {
            WAKE_WORD_TIMEOUT = updateSliderAndInput(this, els.asrWakeupTimeoutValue, this.value);
        });
        els.asrWakeupTimeoutValue.addEventListener('input', function() {
            WAKE_WORD_TIMEOUT = updateSliderAndInput(els.asrWakeupTimeout, this, this.value);
        });
    
        // 测试连接按钮事件
        els.testConnectionBtn?.addEventListener('click', function() {
            const connectionData = getConnectionFormData();
            if (validateConnectionData(connectionData)) {
                testDatabaseConnection(connectionData);
            }
        });
        
        // 保存连接设置按钮事件
        els.saveConnectionBtn?.addEventListener('click', function() {
            const connectionData = getConnectionFormData();
            if (validateConnectionData(connectionData)) {
                saveConnection(connectionData);
            }
        });
        
        // 提交连接按钮事件
        els.submitConnectionBtn?.addEventListener('click', async function() {
            const connectionData = getConnectionFormData();
            if (validateConnectionData(connectionData)) {
                const testSuccess = await testDatabaseConnection(connectionData);
                if (testSuccess) {
                    submitConnection(connectionData);
                    saveConnection(connectionData);
                }
            }
        });
        
        // 文件上传事件
        els.knowledgeFileInput?.addEventListener('change', function(event) {
            if (event.target.files.length > 0) {
                handleFileUpload(event.target.files);
                // 清空input，以便可以再次选择相同文件
                event.target.value = '';
            }
        });
        
        // 处理文件按钮事件
        els.processFilesBtn?.addEventListener('click', processFiles);
        
        // 清空文件列表按钮事件
        els.clearFilesBtn?.addEventListener('click', clearFilesList);

        // // 添加一个变量跟踪按钮物理状态（与录音状态分开）
        // let buttonPhysicallyPressed = false;

        // // 禁用上下文菜单
        // els.voiceRecognitionButton.addEventListener('contextmenu', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     return false;
        // });

        // // 修改事件监听器
        // els.voiceRecognitionButton.addEventListener('mousedown', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     buttonPhysicallyPressed = true;
            
        //     // 立即更新UI
        //     els.voiceRecognitionButton.classList.add('recording');
            
        //     // 异步启动录音但不阻塞事件循环
        //     startRecordingAsync();
        //     return false;
        // });

        // els.voiceRecognitionButton.addEventListener('mouseup', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     buttonPhysicallyPressed = false;
            
        //     // 立即更新UI
        //     els.voiceRecognitionButton.classList.remove('recording');
            
        //     // 如果录音已启动则停止
        //     stopRecordingAsync();
        //     return false;
        // });

        // els.voiceRecognitionButton.addEventListener('mouseleave', function(event) {
        //     if (buttonPhysicallyPressed) {
        //         buttonPhysicallyPressed = false;
        //         els.voiceRecognitionButton.classList.remove('recording');
        //         stopRecordingAsync();
        //     }
        // });

        // // 触摸事件使用相同模式 - 使用捕获阶段以确保最先处理
        // els.voiceRecognitionButton.addEventListener('touchstart', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     buttonPhysicallyPressed = true;
            
        //     // 针对移动端优化：禁止触发长按菜单
        //     if (event.touches && event.touches.length > 0) {
        //         const touch = event.touches[0];
        //         els.voiceRecognitionButton.style.pointerEvents = 'none';
        //         setTimeout(() => {
        //             els.voiceRecognitionButton.style.pointerEvents = 'auto';
        //         }, 100);
        //     }
            
        //     els.voiceRecognitionButton.classList.add('recording');
        //     startRecordingAsync();
        //     return false;
        // }, { passive: false, capture: true });

        // els.voiceRecognitionButton.addEventListener('touchend', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     buttonPhysicallyPressed = false;
            
        //     els.voiceRecognitionButton.classList.remove('recording');
        //     stopRecordingAsync();
        //     return false;
        // }, { passive: false, capture: true });

        // els.voiceRecognitionButton.addEventListener('touchcancel', function(event) {
        //     event.preventDefault();
        //     event.stopPropagation();
        //     if (buttonPhysicallyPressed) {
        //         buttonPhysicallyPressed = false;
        //         els.voiceRecognitionButton.classList.remove('recording');
        //         stopRecordingAsync();
        //     }
        //     return false;
        // }, { passive: false, capture: true });

        // // 添加文档级事件处理，确保释放
        // document.addEventListener('touchend', function() {
        //     if (buttonPhysicallyPressed) {
        //         buttonPhysicallyPressed = false;
        //         els.voiceRecognitionButton.classList.remove('recording');
        //         stopRecordingAsync();
        //     }
        // }, { passive: false });

        // document.addEventListener('mouseup', function() {
        //     if (buttonPhysicallyPressed) {
        //         buttonPhysicallyPressed = false;
        //         els.voiceRecognitionButton.classList.remove('recording');
        //         stopRecordingAsync();
        //     }
        // });

        // // 直接添加兼容性更强的事件绑定方式
        // try {
        //     els.voiceRecognitionButton.ontouchstart = function(e) {
        //         e.preventDefault(); 
        //         buttonPhysicallyPressed = true;
        //         els.voiceRecognitionButton.classList.add('recording');
        //         startRecordingAsync();
        //         return false;
        //     };
            
        //     els.voiceRecognitionButton.ontouchend = function(e) {
        //         e.preventDefault();
        //         buttonPhysicallyPressed = false;
        //         els.voiceRecognitionButton.classList.remove('recording');
        //         stopRecordingAsync();
        //         return false;
        //     };
        // } catch (e) {
        //     console.error('添加备用事件处理失败:', e);
        // }

        // /**
        //  * 异步启动录音，不阻塞事件循环
        //  * 使用try-catch包裹所有操作，确保最大稳定性
        //  */
        // function startRecordingAsync() {
        //     try {
        //         // 如果 ASR 未连接，则连接 ASR
        //         if (!asrConnection) {
        //             connectASRServer();
        //         }
                
        //         // 使用Promise不等待结果，避免阻塞
        //         Promise.resolve().then(() => {
        //             return startASRRecording();
        //         }).catch(error => {
        //             console.error('启动录音失败:', error);
        //             // 失败时恢复状态
        //             els.voiceRecognitionButton.classList.remove('recording');
        //         });
        //     } catch (e) {
        //         console.error('录音启动过程中出错:', e);
        //         els.voiceRecognitionButton.classList.remove('recording');
        //     }
        // }

        // /**
        //  * 异步停止录音，不阻塞事件循环
        //  * 使用try-catch包裹所有操作，确保最大稳定性
        //  */
        // function stopRecordingAsync() {
        //     try {
        //         // 使用Promise不等待结果，避免阻塞
        //         Promise.resolve().then(() => {
        //             return stopASRRecording();
        //         }).catch(error => {
        //             console.error('停止录音失败:', error);
        //         });
        //     } catch (e) {
        //         console.error('录音停止过程中出错:', e);
        //     }
        // }
        // 添加这个函数，它会完全重置按钮的事件处理
    
    }

    function initializeUI() {
        // 初始化页面时设置当前活动标签页的类
        const tabId = els.activeTabButton.getAttribute('data-tab');
        document.body.classList.add(`${tabId}-active`);
        
        // 初始化性能监控组件
        frameCount = 0;
        lastFpsUpdateTime = performance.now();
        
        // 检查性能监控开关状态
        const performanceMonitorSwitch = document.getElementById('showPerformanceStats');
        const performanceStats = document.getElementById('performance-stats');
        
        if (performanceStats && performanceMonitorSwitch) {
            // 根据开关状态设置是否显示
            if (performanceMonitorSwitch.checked) {
                performanceStats.classList.add('visible');
                    } else {
                performanceStats.classList.remove('visible');
            }
        }
        // 添加最大分辨率滑块事件监听
        if (els.maxResolutionSlider && els.maxResolutionValue) {
            // 初始化显示当前值
            els.maxResolutionValue.value = maxCanvasResolution;
            els.maxResolutionSlider.value = maxCanvasResolution;
        }

        // 初始化性能监控状态
        if (els.showPerformanceStats.checked) {
            els.performanceStats.classList.add('visible');
        } else {
            els.performanceStats.classList.remove('visible');
        }

        // 初始化背景图为第一个预设背景
        if (els.bgPresetItems.length > 0) {
            els.bgPresetItems[0].click();
            console.log("初始化背景图为第一个预设背景");
        }

        // 将chatModal的right+bottom定位转换为left+top定位
        if (els.chatModal) {
            // 使用getComputedStyle获取实际计算后的样式值
            const computedStyle = window.getComputedStyle(els.chatModal);
            const width = parseInt(computedStyle.width) || 0;
            const height = parseInt(computedStyle.height) || 0;
            const rightValue = parseInt(computedStyle.right) || 0;
            const bottomValue = parseInt(computedStyle.bottom) || 0;
            
            // 计算left和top值
            const newLeft = window.innerWidth - width - rightValue;
            const newTop = window.innerHeight - height - bottomValue;
            
            // 设置新的left和top值
            els.chatModal.style.left = newLeft + 'px';
            els.chatModal.style.top = newTop + 'px';
            
            // 正确覆盖right和bottom属性
            els.chatModal.style.right = 'auto';
            els.chatModal.style.bottom = 'auto';
            
            console.log("chatModal的right+bottom定位转换为left+top定位");
            console.log("原始尺寸:", {width, height, right: rightValue, bottom: bottomValue});
            console.log("计算后位置:", {left: newLeft, top: newTop});
        }

        // 如果是移动端，点击打开对话模态框
        if (window.innerWidth < 768) {
            els.showChatModalButton.click();
        }
    }
    
    function fixVoiceButtonForProduction() {
        console.log('正在修复语音按钮...');
        
        // 尝试获取按钮和iframe引用
        const voiceButton = document.getElementById('voice-recognition-button');
        
        if (!voiceButton) {
            console.error('找不到语音按钮元素');
            return;
        }
        
        // 移除所有现有事件监听器
        const newButton = voiceButton.cloneNode(true);
        if (voiceButton.parentNode) {
            voiceButton.parentNode.replaceChild(newButton, voiceButton);
        }
        
        // 录音状态标志
        let isRecording = false;

        newButton.oncontextmenu = function(e) {
            e.preventDefault();
            console.log('禁止长按选择文本');
        };
        
        // 直接使用事件属性绑定 - 最基本的方法
        newButton.onmousedown = function(e) {
            if (e) e.preventDefault();
            console.log('按下按钮，开始录音');
            this.classList.add('recording');
            isRecording = true;
            // 停止此时的 VAD
            pauseVADIfActive();

            // 暂时移除按钮的 data-ui 属性
            this.removeAttribute('data-ui');
            updateElement('uiElements', document.querySelectorAll('[data-ui]'));
            // console.log("当前uiElements:", els.uiElements);
            
            if (!asrConnection) connectASRServer();
            startASRRecording();
        };
        
        newButton.onmouseup = function(e) {
            if (e) e.preventDefault();
            console.log('松开按钮，停止录音');
            this.classList.remove('recording');
            isRecording = false;

            // 恢复此时的 VAD
            resumeVADIfPaused();

            // 恢复按钮的 data-ui 属性
            this.setAttribute('data-ui', 'ui');
            updateElement('uiElements', document.querySelectorAll('[data-ui]'));
            // console.log("当前uiElements:", els.uiElements);
            
            stopASRRecording();
        };
        
        newButton.onmouseleave = newButton.onmouseup;
        
        // 触摸事件
        newButton.ontouchstart = function(e) {
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            console.log('触摸开始，开始录音');
            this.classList.add('recording');
            isRecording = true;
            // 停止此时的 VAD
            pauseVADIfActive();

            // 暂时移除按钮的 data-ui 属性
            this.removeAttribute('data-ui');
            updateElement('uiElements', document.querySelectorAll('[data-ui]'));
            // console.log("当前uiElements:", els.uiElements);
            
            if (!asrConnection) connectASRServer();
            startASRRecording();
                
            return false;
        };
        
        newButton.ontouchend = function(e) {
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            console.log('触摸结束，停止录音');
            this.classList.remove('recording');
            isRecording = false;

            // 恢复此时的 VAD
            resumeVADIfPaused();

            // 恢复按钮的 data-ui 属性
            this.setAttribute('data-ui', 'ui');
            updateElement('uiElements', document.querySelectorAll('[data-ui]'));
            // console.log("当前uiElements:", els.uiElements);
            
            stopASRRecording();

            return false;
        };
        
        newButton.ontouchcancel = newButton.ontouchend;

        // 由于该按钮时 data-ui 类型，所以需要重新获取所有data-ui类型的dom
        updateElement('uiElements', document.querySelectorAll('[data-ui]'));
        console.log("当前uiElements:", els.uiElements);
    
        console.log('语音按钮修复完成');
    }

    /**
     * 设置Canvas尺寸 - 通用方法，支持平滑动画
     * @param {number} width - 要设置的宽度
     * @param {number} height - 要设置的高度
     * @param {boolean} [forceUpdate=false] - 是否强制更新尺寸，忽略未设置检查
     * @param {boolean} [limitMaxSize=false] - 是否限制最大尺寸
     * @param {number} [maxHeight=maxCanvasResolution] - 最大高度限制
     * @param {string} [anchorPoint='center'] - 尺寸变化时的锚点：'center'(中心点), 'top-left'(左上), 'top-right'(右上), 'bottom-left'(左下), 'bottom-right'(右下)
     * @param {number|null} [animationSpeed=null] - 动画速度，每帧尺寸变化的百分比。null或0表示不使用动画（立即变化），如需使用动画请指定大于0的值，如0.1表示每帧变化10%
     * @param {Function} [callback=null] - 动画完成后的回调函数
     * @returns {Object} 设置后的尺寸信息
     */
    function setupCanvasSize(width, height, forceUpdate = false, limitMaxSize = false, maxHeight = maxCanvasResolution, anchorPoint = 'center', animationSpeed = null, callback = null) {
        // 参数校验，如果宽高为0，则不更新尺寸
        if (width <= 0 || height <= 0) {
            console.error("setupCanvasSize: 无效的宽度或高度参数");
            return null;
        }
        
        // 如果宽高不为数值，则转换为数值
        width = parseInt(width);
        height = parseInt(height);

        // 从els对象获取DOM元素
        const canvas = els.canvas;
        
        // 检查输入参数
        if (typeof width !== 'number' || width <= 0 || typeof height !== 'number' || height <= 0) {
            console.error("setupCanvasSize: 无效的宽度或高度参数");
            return null;
        }
        
        // 判断是否需要更新尺寸
        const needsUpdate = forceUpdate || 
                            !canvas.style.width || 
                            !canvas.style.height || 
                            parseInt(canvas.style.width) !== width || 
                            parseInt(canvas.style.height) !== height;
        
        if (needsUpdate) {
            // 获取当前尺寸和位置，用于计算锚点偏移
            const currentWidth = canvas.style.width ? parseInt(canvas.style.width) : width;
            const currentHeight = canvas.style.height ? parseInt(canvas.style.height) : height;
            const currentLeft = canvas.style.left ? parseInt(canvas.style.left) : 0;
            const currentTop = canvas.style.top ? parseInt(canvas.style.top) : 0;
            
            // 使用传入的尺寸
            let finalWidth = width;
            let finalHeight = height;
            
            // 限制最大尺寸（如果需要）
            if (limitMaxSize && (finalWidth > maxCanvasResolution || finalHeight > maxCanvasResolution)) {
                const scaleFactor = maxCanvasResolution / Math.max(finalWidth, finalHeight);
                const originalWidth = finalWidth;
                const originalHeight = finalHeight;
                finalWidth = Math.floor(finalWidth * scaleFactor);
                finalHeight = Math.floor(finalHeight * scaleFactor);
                console.log(`尺寸超限，等比例缩放: ${originalWidth}x${originalHeight} -> ${finalWidth}x${finalHeight}`);
            }
            
            // 计算尺寸变化量
            const widthDiff = finalWidth - currentWidth;
            const heightDiff = finalHeight - currentHeight;
            
            // 根据锚点计算目标位置
            let targetLeft = currentLeft;
            let targetTop = currentTop;
            
            if (currentWidth > 0 && currentHeight > 0) { // 确保有当前尺寸才计算偏移
                switch (anchorPoint) {
                    case 'top-left':
                        // 左上角固定，不需要调整位置
                        break;
                        
                    case 'top-right':
                        // 右上角固定，向左移动宽度差
                        targetLeft = currentLeft - widthDiff;
                        break;
                        
                    case 'bottom-left':
                        // 左下角固定，向上移动高度差
                        targetTop = currentTop - heightDiff;
                        break;
                        
                    case 'bottom-right':
                        // 右下角固定，向左上移动宽度差和高度差
                        targetLeft = currentLeft - widthDiff;
                        targetTop = currentTop - heightDiff;
                        break;
                        
                    case 'center':
                    default:
                        // 中心点固定，向左上移动宽度差和高度差的一半
                        targetLeft = currentLeft - (widthDiff / 2);
                        targetTop = currentTop - (heightDiff / 2);
                        break;
                }
            }
            
            // 计算渲染尺寸，考虑最大分辨率限制
            let finalRenderWidth = finalWidth;
            let finalRenderHeight = finalHeight;
            
            if (finalRenderWidth > maxCanvasResolution || finalRenderHeight > maxCanvasResolution) {
                const scaleFactor = maxCanvasResolution / Math.max(finalRenderWidth, finalRenderHeight);
                finalRenderWidth = Math.floor(finalRenderWidth * scaleFactor);
                finalRenderHeight = Math.floor(finalRenderHeight * scaleFactor);
                console.log(`最终渲染分辨率已限制: ${finalRenderWidth}x${finalRenderHeight}，显示尺寸: ${finalWidth}x${finalHeight}`);
            }
            
            // 判断是否使用动画
            // 当变化非常小或明确指定不使用动画时，直接应用变化
            if ((Math.abs(widthDiff) < 2 && Math.abs(heightDiff) < 2) || animationSpeed === 0 || animationSpeed === null) {
                // 直接应用最终尺寸和位置
                applyCanvasSize(finalWidth, finalHeight, targetLeft, targetTop);
                
                // 设置渲染尺寸
                finalizeCanvasSize(finalRenderWidth, finalRenderHeight);
                
                // 触发回调
                if (typeof callback === 'function') {
                    callback({
                        width: finalWidth,
                        height: finalHeight,
                        left: targetLeft,
                        top: targetTop,
                        renderWidth: finalRenderWidth,
                        renderHeight: finalRenderHeight
                    });
                }
                
                // 返回最终尺寸和位置信息
                return {
                    width: finalWidth,
                    height: finalHeight,
                    left: targetLeft,
                    top: targetTop,
                    renderWidth: finalRenderWidth,
                    renderHeight: finalRenderHeight
                };
            }
            
            // 避免多个动画同时运行
            if (window._sizeAnimationId) {
                cancelAnimationFrame(window._sizeAnimationId);
                window._sizeAnimationId = null;
            }
            
            // 动画相关变量
            let lastTimestamp = null;
            let animCurrentWidth = currentWidth;
            let animCurrentHeight = currentHeight;
            let animCurrentLeft = currentLeft;
            let animCurrentTop = currentTop;
            let animCurrentRenderWidth = canvas.width;
            let animCurrentRenderHeight = canvas.height;
            
            // 动画函数
            function animateSize(timestamp) {
                if (!lastTimestamp) {
                    lastTimestamp = timestamp;
                }
                
                // 计算从上一帧到现在的时间差
                const elapsed = timestamp - lastTimestamp;
                lastTimestamp = timestamp;
                
                // 基于60fps调整动画速度
                const frameAdjustment = elapsed / 16.67;
                const speed = Math.min(0.3, animationSpeed * frameAdjustment); // 限制最大速度，防止跳变
                
                // 计算本帧的尺寸和位置（线性插值）
                let completed = true; // 判断是否所有属性都已达到目标值
                
                // 宽度动画
                const widthRemaining = finalWidth - animCurrentWidth;
                if (Math.abs(widthRemaining) > 0.5) {
                    animCurrentWidth += widthRemaining * speed;
                    completed = false;
                } else {
                    animCurrentWidth = finalWidth;
                }
                
                // 高度动画
                const heightRemaining = finalHeight - animCurrentHeight;
                if (Math.abs(heightRemaining) > 0.5) {
                    animCurrentHeight += heightRemaining * speed;
                    completed = false;
                } else {
                    animCurrentHeight = finalHeight;
                }
                
                // 左侧位置动画
                const leftRemaining = targetLeft - animCurrentLeft;
                if (Math.abs(leftRemaining) > 0.5) {
                    animCurrentLeft += leftRemaining * speed;
                    completed = false;
                } else {
                    animCurrentLeft = targetLeft;
                }
                
                // 顶部位置动画
                const topRemaining = targetTop - animCurrentTop;
                if (Math.abs(topRemaining) > 0.5) {
                    animCurrentTop += topRemaining * speed;
                    completed = false;
                } else {
                    animCurrentTop = targetTop;
                }
                
                // 渲染宽度动画
                const renderWidthRemaining = finalRenderWidth - animCurrentRenderWidth;
                if (Math.abs(renderWidthRemaining) > 0.5) {
                    animCurrentRenderWidth += renderWidthRemaining * speed;
                    completed = false;
                } else {
                    animCurrentRenderWidth = finalRenderWidth;
                }
                
                // 渲染高度动画
                const renderHeightRemaining = finalRenderHeight - animCurrentRenderHeight;
                if (Math.abs(renderHeightRemaining) > 0.5) {
                    animCurrentRenderHeight += renderHeightRemaining * speed;
                    completed = false;
                } else {
                    animCurrentRenderHeight = finalRenderHeight;
                }
                
                // 应用当前帧的尺寸和位置
                applyCanvasSize(
                    Math.round(animCurrentWidth), 
                    Math.round(animCurrentHeight), 
                    Math.round(animCurrentLeft), 
                    Math.round(animCurrentTop)
                );
                
                // 如果动画完成，则清理并触发回调
                if (completed) {
                    window._sizeAnimationId = null;
                    
                    // 确保最终值精确匹配目标值
                    applyCanvasSize(finalWidth, finalHeight, targetLeft, targetTop);

                    // 新增：动画完成后最终设置渲染尺寸
                    finalizeCanvasSize(finalRenderWidth, finalRenderHeight);
                    
                    // 触发回调
                    if (typeof callback === 'function') {
                        callback({
                            width: finalWidth,
                            height: finalHeight,
                            left: targetLeft,
                            top: targetTop,
                            renderWidth: finalRenderWidth,
                            renderHeight: finalRenderHeight
                        });
                    }
                    
                    return;
                }
                
                // 继续下一帧动画
                window._sizeAnimationId = requestAnimationFrame(animateSize);
            }
            
            // 启动动画
            window._sizeAnimationId = requestAnimationFrame(animateSize);
            
            // 返回最终目标尺寸和位置信息（动画中的实际值会逐渐接近这些值）
            return {
                width: finalWidth,
                height: finalHeight,
                left: targetLeft,
                top: targetTop,
                renderWidth: finalRenderWidth,
                renderHeight: finalRenderHeight,
                animated: true
            };
        } else {
            console.log("无需更新Canvas尺寸");
            
            // 返回当前尺寸和位置
            return { 
                width: parseInt(canvas.style.width), 
                height: parseInt(canvas.style.height),
                left: parseInt(canvas.style.left),
                top: parseInt(canvas.style.top),
                renderWidth: canvas.width,
                renderHeight: canvas.height,
                animated: false
            };
        }
        
        // 助手函数：应用Canvas尺寸和位置并更新UI
        function applyCanvasSize(width, height, left, top) {
            // 设置显示尺寸
            canvas.style.width = width + 'px';
            canvas.style.height = height + 'px';
            // 设置位置
            canvas.style.left = left + 'px';
            canvas.style.top = top + 'px';
            
            // 更新全局变量
            // customWidth = width;
            // customHeight = height;
            // dhOffset.x = left;
            // dhOffset.y = top;
            
            // 更新UI控件
            dhSize.width = updateSliderAndInput(els.customWidthSlider, els.customWidthValue, width);
            dhSize.height = updateSliderAndInput(els.customHeightSlider, els.customHeightValue, height);
            dhOffset.x = updateSliderAndInput(els.xOffsetSlider, els.xOffsetValue, left);
            dhOffset.y = updateSliderAndInput(els.yOffsetSlider, els.yOffsetValue, top);
        }

        // 只在动画完成时一次性设置渲染尺寸
        function finalizeCanvasSize(renderWidth, renderHeight) {
            // 只在真正需要改变渲染尺寸时执行
            if (canvas.width !== renderWidth || canvas.height !== renderHeight) {
                canvas.width = renderWidth;
                canvas.height = renderHeight;
            }
        }
    }

    /**
     * 基础定位方法
     * @param {HTMLElement} element - 要定位的元素
     * @param {Object} offsets - 偏移量对象
     * @param {Object} changeOffsetElements - UI控件元素
     * @param {number} positionX - 目标X坐标
     * @param {number} positionY - 目标Y坐标
     * @param {number|null} animationSpeed - 动画速度
     * @param {Function} callback - 回调函数
     * @returns {Object} 位置信息
     */
    function positionElement(element, offsets, changeOffsetElements, positionX, positionY, animationSpeed = null, callback = null) {
        // 确保位置为整数，避免模糊
        const targetLeft = Math.round(positionX);
        const targetTop = Math.round(positionY);
        
        // 获取当前位置
        const currentLeft = parseInt(element.style.left) || 0;
        const currentTop = parseInt(element.style.top) || 0;
        
        // 计算移动距离
        const deltaX = targetLeft - currentLeft;
        const deltaY = targetTop - currentTop;
        
        // 如果没有距离需要移动，或指定不使用动画，则直接设置位置
        if ((deltaX === 0 && deltaY === 0) || animationSpeed === 0 || animationSpeed === null) {
            applyPosition(element, targetLeft, targetTop, offsets, changeOffsetElements);
            
            if (typeof callback === 'function') {
                callback({
                    previouswidth: element.width,
                    previousheight: element.height,
                    previousLeft: currentLeft,
                    previousTop: currentTop
                });
            }
            
            return {
                previouswidth: element.width,
                previousheight: element.height,
                previousLeft: currentLeft,
                previousTop: currentTop,
            };
        }

        // 在计算完成后立即更新offsets对象
        offsets.x = targetLeft;
        offsets.y = targetTop;
        
        // 避免多个动画同时运行
        if (window._positionAnimationId) {
            cancelAnimationFrame(window._positionAnimationId);
            window._positionAnimationId = null;
        }
        
        // 动画相关变量
        let lastTimestamp = null;
        let animCurrentLeft = currentLeft;
        let animCurrentTop = currentTop;
        
        // 动画函数
        function animate(timestamp) {
            if (!lastTimestamp) {
                lastTimestamp = timestamp;
            }
            
            // 计算从上一帧到现在的时间差
            const elapsed = timestamp - lastTimestamp;
            lastTimestamp = timestamp;
            
            // 基于60fps调整动画速度
            const frameAdjustment = elapsed / 16.67;
            const speed = Math.min(0.3, animationSpeed * frameAdjustment);
            
            // 计算本帧的位置（线性插值）
            let completed = true;
            
            // 水平位置动画
            const leftRemaining = targetLeft - animCurrentLeft;
            if (Math.abs(leftRemaining) > 0.5) {
                animCurrentLeft += leftRemaining * speed;
                completed = false;
            } else {
                animCurrentLeft = targetLeft;
            }
            
            // 垂直位置动画
            const topRemaining = targetTop - animCurrentTop;
            if (Math.abs(topRemaining) > 0.5) {
                animCurrentTop += topRemaining * speed;
                completed = false;
            } else {
                animCurrentTop = targetTop;
            }
            
            // 应用当前帧的位置
            applyPosition(element, Math.round(animCurrentLeft), Math.round(animCurrentTop), offsets, changeOffsetElements);
            
            // 如果动画完成，则清理并触发回调
            if (completed) {
                window._positionAnimationId = null;
                
                // 确保最终值精确匹配目标值
                applyPosition(element, targetLeft, targetTop, offsets, changeOffsetElements);
                
                // 触发回调
                if (typeof callback === 'function') {
                    callback({
                        previouswidth: element.width,
                        previousheight: element.height,
                        previousLeft: currentLeft,
                        previousTop: currentTop
                    });
                }
                
                return;
            }
            
            // 继续下一帧动画
            window._positionAnimationId = requestAnimationFrame(animate);
        }

        
        /**
         * 应用位置并更新UI
         */
        function applyPosition(element, left, top, offsets, changeOffsetElements) {
            // 设置新位置
            element.style.left = left + 'px';
            element.style.top = top + 'px';
            
            // 更新UI控件
            updateSliderAndInput(changeOffsetElements.xOffsetSlider, changeOffsetElements.xOffsetValue, left);
            updateSliderAndInput(changeOffsetElements.yOffsetSlider, changeOffsetElements.yOffsetValue, top);
        } 
        
        // 启动动画
        window._positionAnimationId = requestAnimationFrame(animate);
        
        // 返回目标位置信息
        return {
            previouswidth: element.width,
            previousheight: element.height,
            previousLeft: currentLeft,
            previousTop: currentTop,
        };
    }


    /**
     * 定位数字人Canvas，支持平滑动画
     * @param {HTMLElement} element - 要定位的元素
     * @param {Object} offsets - 偏移量对象
     * @param {Object} changeOffsetElements - UI控件元素
     * @param {string} positionMode - 定位模式: 'left-bottom', 'right-bottom', 'center', 'bottom'等
     * @param {number} marginX - X轴边距(像素)
     * @param {number} marginY - Y轴边距(像素)
     * @param {number|null} animationSpeed - 动画速度
     * @param {Function} callback - 回调函数
     */
    function positionModeElement(element, offsets, changeOffsetElements, positionMode = 'center', marginX = 0, marginY = 0, animationSpeed = null, callback = null) {
        // 从els对象获取DOM元素
        const mediaElement = els.mediaElement;
        
        // 检查canvas是否已有尺寸
        if (!element.style.width || !element.style.height) {
            // 获取
            console.error("Canvas尺寸未设置，无法定位");
            return null;
        }
        
        // 获取canvas当前尺寸
        const elementWidth = parseInt(element.style.width);
        const elementHeight = parseInt(element.style.height);
        
        // 获取容器尺寸
        const containerRect = mediaElement.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        // 计算目标位置
        let targetLeft, targetTop;
        
        switch (positionMode) {
            case 'left-top':
                targetLeft = marginX;
                targetTop = marginY;
                console.log("左上角定位:", targetLeft, targetTop);
                break;

            case 'left-bottom':
                targetLeft = marginX;
                targetTop = containerHeight - elementHeight - marginY;
                console.log("左下角定位:", targetLeft, targetTop);
                break;
            
            case 'left-center':
                targetLeft = marginX;
                targetTop = (containerHeight - elementHeight) / 2;
                console.log("左中定位:", targetLeft, targetTop);
                break;
                
            case 'right-top':
                targetLeft = containerWidth - elementWidth - marginX;
                targetTop = marginY;
                console.log("右上角定位:", targetLeft, targetTop);
                break;

            case 'right-bottom':
                targetLeft = containerWidth - elementWidth - marginX;
                targetTop = containerHeight - elementHeight - marginY;
                console.log("右下角定位:", targetLeft, targetTop);
                break;
                
            case 'right-center':
                targetLeft = containerWidth - elementWidth - marginX;
                targetTop = (containerHeight - elementHeight) / 2;
                console.log("右中定位:", targetLeft, targetTop);
                break;

            case 'center-top':
                targetLeft = (containerWidth - elementWidth) / 2;
                targetTop = marginY;
                console.log("顶部居中定位:", targetLeft, targetTop);
                break;

            case 'center-bottom':
                targetLeft = (containerWidth - elementWidth) / 2;
                targetTop = containerHeight - elementHeight - marginY;
                console.log("底部居中定位:", targetLeft, targetTop);
                break;
                
            case 'center':
            default:
                targetLeft = (containerWidth - elementWidth) / 2;
                targetTop = (containerHeight - elementHeight) / 2;
                console.log("中心定位:", targetLeft, targetTop);
                break;
        }
        
        // 使用基础定位方法
        const previousResult = positionElement(element, offsets, changeOffsetElements, targetLeft, targetTop, animationSpeed, callback);
        
        // 添加模式信息到返回结果
        return previousResult;
    }

    /**
     * 一键调整数字人位置和大小，同时执行动画
     * @param {HTMLElement} element - 要调整的元素
     * @param {string} positionMode - 定位模式: 'left-bottom', 'right-bottom', 'center', 'bottom'等
     * @param {Object} offsets - 偏移量对象，会被更新
     * @param {Object} size - 尺寸对象，会被更新
     * @param {Object} changeElements - UI控件元素对象
     * @param {number} [marginX=0] - X轴边距(像素)
     * @param {number} [marginY=0] - Y轴边距(像素)
     * @param {number|null} [animationSpeed=0.1] - 动画速度，null或0表示瞬间变化
     * @param {Function} [callback=null] - 动画完成后的回调函数
     * @param {number} [targetWidth=null] - 目标宽度，如果提供则覆盖size.width
     * @param {number} [targetHeight=null] - 目标高度，如果提供则覆盖size.height
     * @param {number} [targetLeft=null] - 目标左位置，如果提供则覆盖offsets.x
     * @param {number} [targetTop=null] - 目标上位置，如果提供则覆盖offsets.y
     * @returns {Object} 包含位置和尺寸信息的对象
     */
    function positionAndResizeElement(element, positionMode, offsets, size, changeElements, marginX = 0, marginY = 0, animationSpeed = 0.1, callback = null, targetWidth = null, targetHeight = null, targetLeft = null, targetTop = null) {
        // 使用目标宽度(如果提供)，否则使用size.width
        const width = targetWidth !== null ? targetWidth : size.width;
        
        // 检查参数
        if (width === null) {
            console.error("缺少必要参数");
            return null;
        }

        // 将目标位置和大小参数转换为整数（处理null值）
        targetWidth = targetWidth !== null ? Math.round(targetWidth) : null;
        targetHeight = targetHeight !== null ? Math.round(targetHeight) : null;
        targetLeft = targetLeft !== null ? Math.round(targetLeft) : null;
        targetTop = targetTop !== null ? Math.round(targetTop) : null;
        marginX = Math.round(marginX);
        marginY = Math.round(marginY);
        
        // 从els对象获取DOM元素
        const mediaElement = els.mediaElement;
        
        // 获取canvas当前尺寸和位置
        const currentWidth = parseInt(element.style.width) || initialWidth;
        const currentHeight = parseInt(element.style.height) || initialHeight;
        const currentLeft = parseInt(element.style.left) || 0;
        const currentTop = parseInt(element.style.top) || 0;
        
        // 计算目标高度
        let finalHeight = targetHeight;
        if (finalHeight === null) {
            if (size.height !== null && targetWidth === null) {
                // 如果没有指定目标高度和宽度，使用size.height
                finalHeight = size.height;
            } else {
            // 使用当前比例计算高度
            const currentRatio = currentHeight / currentWidth;
                finalHeight = Math.round(width * currentRatio);
            }
            // 如果是custom模式且提供了明确的targetHeight，则直接使用
            if (positionMode === 'custom' && targetHeight !== null) {
                finalHeight = targetHeight;
            }
        }

        
        // 获取容器尺寸
        const containerRect = mediaElement.getBoundingClientRect();
        const containerWidth = containerRect.width;
        const containerHeight = containerRect.height;
        
        // 计算目标位置
        // let targetLeft, targetTop;
        
        switch (positionMode) {
            case 'left-bottom':
                // 左下角定位 - canvas的左下角与容器左下角对齐(考虑边距)
                targetLeft = marginX;
                targetTop = containerHeight - finalHeight - marginY;
                console.log("左下角定位:", targetLeft, targetTop);
                break;
                
            case 'right-bottom':
                // 右下角定位 - canvas的右下角与容器右下角对齐(考虑边距)
                targetLeft = containerWidth - width - marginX;
                targetTop = containerHeight - finalHeight - marginY;
                console.log("右下角定位:", targetLeft, targetTop);
                break;
                
            case 'center-bottom':
                // 底部居中 - canvas底边居中对齐容器底边(考虑边距)
                targetLeft = (containerWidth - width) / 2;
                targetTop = containerHeight - finalHeight - marginY;
                console.log("底部居中定位:", targetLeft, targetTop);
                break;
                
            case 'center':
                // 完全居中 - canvas中心与容器中心对齐
                targetLeft = (containerWidth - width) / 2;
                targetTop = (containerHeight - finalHeight) / 2;
                console.log("中心定位:", targetLeft, targetTop);
                break;
        
            case 'left-top':
                // 左上角定位 - canvas的左上角与容器左上角对齐(考虑边距)
                targetLeft = marginX;
                targetTop = marginY;
                console.log("左上角定位:", targetLeft, targetTop);
                break;
        
            case 'left-center':
                // 左中定位 - canvas左边沿垂直居中对齐容器左边沿(考虑边距)
                targetLeft = marginX;
                targetTop = (containerHeight - finalHeight) / 2;
                console.log("左中定位:", targetLeft, targetTop);
                break;
        
            case 'top':
                // 顶部居中 - canvas顶边居中对齐容器顶边(考虑边距)
                targetLeft = (containerWidth - width) / 2;
                targetTop = marginY;
                console.log("顶部居中定位:", targetLeft, targetTop);
                break;
        
            case 'right-top':
                // 右上角定位 - canvas的右上角与容器右上角对齐(考虑边距)
                targetLeft = containerWidth - width - marginX;
                targetTop = marginY;
                console.log("右上角定位:", targetLeft, targetTop);
                break;
        
            case 'right-center':
                // 右中定位 - canvas右边沿垂直居中对齐容器右边沿(考虑边距)
                targetLeft = containerWidth - width - marginX;
                targetTop = (containerHeight - finalHeight) / 2;
                console.log("右中定位:", targetLeft, targetTop);
                break;
        
            case 'top-center':
                // 正上定位 - canvas顶边水平居中对齐容器顶边(考虑边距)
                targetLeft = (containerWidth - width) / 2;
                targetTop = marginY;
                console.log("正上定位:", targetLeft, targetTop);
                break;
            case 'custom':
                // 自定义位置，使用传入的targetLeft和targetTop值
                // 如果明确提供了目标位置则使用，否则保持原位置不变
                if (targetLeft === null) targetLeft = currentLeft;
                if (targetTop === null) targetTop = currentTop;
                console.log("自定义位置:", targetLeft, targetTop);
                break;
            default:
                break;
        }
        
        // 确保位置为整数，避免模糊
        targetLeft = Math.round(targetLeft);
        targetTop = Math.round(targetTop);
        
        // 计算渲染尺寸（考虑最大分辨率限制）
        let renderWidth = width;
        let renderHeight = finalHeight;
        
        if (renderWidth > maxCanvasResolution || renderHeight > maxCanvasResolution) {
            const scaleFactor = maxCanvasResolution / Math.max(renderWidth, renderHeight);
            renderWidth = Math.floor(renderWidth * scaleFactor);
            renderHeight = Math.floor(renderHeight * scaleFactor);
        }
        
        // 检查变化幅度
        const sizeChangeTooSmall = Math.abs(width - currentWidth) < 2 && Math.abs(finalHeight - currentHeight) < 2;
        const positionChangeTooSmall = Math.abs(targetLeft - currentLeft) < 2 && Math.abs(targetTop - currentTop) < 2;
        
        // 如果变化很小或明确指定不使用动画，则直接应用
        if ((sizeChangeTooSmall && positionChangeTooSmall) || animationSpeed === 0 || animationSpeed === null) {
            // 应用尺寸和位置
            element.style.width = width + 'px';
            element.style.height = finalHeight + 'px';
            element.style.left = targetLeft + 'px';
            element.style.top = targetTop + 'px';
            
            // 更新size和offsets对象
            size.width = width;
            size.height = finalHeight;
            offsets.x = targetLeft;
            offsets.y = targetTop;
            
            // 更新UI控件
            size.width = updateSliderAndInput(changeElements.widthSlider, changeElements.widthValue, width);
            size.height = updateSliderAndInput(changeElements.heightSlider, changeElements.heightValue, finalHeight);
            offsets.x = updateSliderAndInput(changeElements.xOffsetSlider, changeElements.xOffsetValue, targetLeft);
            offsets.y = updateSliderAndInput(changeElements.yOffsetSlider, changeElements.yOffsetValue, targetTop);
            
            // 设置实际渲染尺寸
            if (element.width !== renderWidth || element.height !== renderHeight) {
                element.width = renderWidth;
                element.height = renderHeight;
            }
            
            // 触发回调
            if (typeof callback === 'function') {
                callback({
                    previousWidth: currentWidth,
                    previousHeight: currentHeight,
                    previousLeft: currentLeft,
                    previousTop: currentTop
                });
            }
            
            return {
                previousWidth: currentWidth,
                previousHeight: currentHeight,
                previousLeft: currentLeft,
                previousTop: currentTop
            };
        }
        
        // 避免多个动画同时运行
        if (window._combinedAnimationId) {
            cancelAnimationFrame(window._combinedAnimationId);
            window._combinedAnimationId = null;
        }
        
        // 动画相关变量
        let lastTimestamp = null;
        let animCurrentWidth = currentWidth;
        let animCurrentHeight = currentHeight;
        let animCurrentLeft = currentLeft;
        let animCurrentTop = currentTop;
        
        // 动画函数
        function animateCombined(timestamp) {
            if (!lastTimestamp) {
                lastTimestamp = timestamp;
            }
            
            // 计算从上一帧到现在的时间差
            const elapsed = timestamp - lastTimestamp;
            lastTimestamp = timestamp;
            
            // 基于60fps调整动画速度
            const frameAdjustment = elapsed / 16.67; // 60fps下一帧约16.67ms
            const speed = Math.min(0.3, animationSpeed * frameAdjustment); // 限制最大速度，防止跳变
            
            // 计算本帧的尺寸和位置（线性插值）
            let completed = true; // 判断是否所有属性都已达到目标值
            
            // 宽度动画
            const widthRemaining = width - animCurrentWidth;
            if (Math.abs(widthRemaining) > 0.5) {
                animCurrentWidth += widthRemaining * speed;
                completed = false;
            } else {
                animCurrentWidth = width;
            }
            
            // 高度动画
            const heightRemaining = finalHeight - animCurrentHeight;
            if (Math.abs(heightRemaining) > 0.5) {
                animCurrentHeight += heightRemaining * speed;
                completed = false;
            } else {
                animCurrentHeight = finalHeight;
            }
            
            // 左侧位置动画
            const leftRemaining = targetLeft - animCurrentLeft;
            if (Math.abs(leftRemaining) > 0.5) {
                animCurrentLeft += leftRemaining * speed;
                completed = false;
            } else {
                animCurrentLeft = targetLeft;
            }
            
            // 顶部位置动画
            const topRemaining = targetTop - animCurrentTop;
            if (Math.abs(topRemaining) > 0.5) {
                animCurrentTop += topRemaining * speed;
                completed = false;
            } else {
                animCurrentTop = targetTop;
            }
            
            // 应用当前帧的尺寸和位置
            element.style.width = Math.round(animCurrentWidth) + 'px';
            element.style.height = Math.round(animCurrentHeight) + 'px';
            element.style.left = Math.round(animCurrentLeft) + 'px';
            element.style.top = Math.round(animCurrentTop) + 'px';
            
            // 更新UI控件
            size.width = updateSliderAndInput(changeElements.widthSlider, changeElements.widthValue, Math.round(animCurrentWidth));
            size.height = updateSliderAndInput(changeElements.heightSlider, changeElements.heightValue, Math.round(animCurrentHeight));
            offsets.x = updateSliderAndInput(changeElements.xOffsetSlider, changeElements.xOffsetValue, Math.round(animCurrentLeft));
            offsets.y = updateSliderAndInput(changeElements.yOffsetSlider, changeElements.yOffsetValue, Math.round(animCurrentTop));
            
            // 如果动画完成，则清理并触发回调
            if (completed) {
                window._combinedAnimationId = null;
                
                // 确保最终值精确匹配目标值
                element.style.width = width + 'px';
                element.style.height = finalHeight + 'px';
                element.style.left = targetLeft + 'px';
                element.style.top = targetTop + 'px';
                
                // 更新全局变量和size、offsets对象
                size.width = width;
                size.height = finalHeight;
                offsets.x = targetLeft;
                offsets.y = targetTop;
                
                // 更新UI控件（确保最终值准确）
                size.width = updateSliderAndInput(changeElements.widthSlider, changeElements.widthValue, width);
                size.height = updateSliderAndInput(changeElements.heightSlider, changeElements.heightValue, finalHeight);
                offsets.x = updateSliderAndInput(changeElements.xOffsetSlider, changeElements.xOffsetValue, targetLeft);
                offsets.y = updateSliderAndInput(changeElements.yOffsetSlider, changeElements.yOffsetValue, targetTop);
                
                // 最后设置实际渲染尺寸
                if (element.width !== renderWidth || element.height !== renderHeight) {
                    element.width = renderWidth;
                    element.height = renderHeight;
                }
                
                // 触发回调
                if (typeof callback === 'function') {
                    callback({
                        previousWidth: currentWidth,
                        previousHeight: currentHeight,
                        previousLeft: currentLeft,
                        previousTop: currentTop
                    });
                }
                
                return;
            }
            
            // 继续下一帧动画
            window._combinedAnimationId = requestAnimationFrame(animateCombined);
        }
        
        // 启动动画
        window._combinedAnimationId = requestAnimationFrame(animateCombined);
        
        // 返回目标信息（动画中的实际值会逐渐接近这些值）
        return {
            previousWidth: currentWidth,
            previousHeight: currentHeight,
            previousLeft: currentLeft,
            previousTop: currentTop
        };
    }

    /**
     * 计算并设置Canvas居中位置
     */
    function centerCanvasInContainer() {
        // 从els对象获取DOM元素
        const canvas = els.canvas;
        const mediaElement = els.mediaElement;

        // 获取canvas当前尺寸
        const canvasWidth = parseInt(canvas.style.width);
        const canvasHeight = parseInt(canvas.style.height);

        // 获取容器尺寸和位置
        const mediaDivRect = mediaElement.getBoundingClientRect();

        // 计算居中位置
        const centerX = Math.round((mediaDivRect.width - canvasWidth) / 2);
        const centerY = Math.round((mediaDivRect.height - canvasHeight) / 2);

        
        console.log("mediaDivRect.width:",mediaDivRect.width);
        console.log("mediaDivRect.height:",mediaDivRect.height);
        
        console.log("canvasWidth:",canvasWidth);
        console.log("canvasHeight:",canvasHeight);

        console.log("centerX:",centerX);
        console.log("centerY:",centerY);
        

        // 设置canvas位置
        canvas.style.left = centerX + 'px';
        canvas.style.top = centerY + 'px';

        // 更新位置滑块
        dhOffset.x = updateSliderAndInput(els.xOffsetSlider, els.xOffsetValue, centerX);
        dhOffset.y = updateSliderAndInput(els.yOffsetSlider, els.yOffsetValue, centerY);


        // 更新xOffset和yOffset变量
        dhOffset.x = centerX;
        dhOffset.y = centerY;

        return { x: centerX, y: centerY };
    }

    // 回正数字人
    function resetDigitalHuman() {
        // 获取容器尺寸
        const containerRect = els.mediaElement.getBoundingClientRect();
        const containerHeight = Math.round(containerRect.height);
        
        // 计算宽度（保持原始比例）
        // const currentWidth = parseInt(els.canvas.style.width) || initialWidth;
        // const currentHeight = parseInt(els.canvas.style.height) || initialHeight;
        // const ratio = currentWidth / currentHeight;
        const targetWidth = Math.round(containerHeight * aspectRatio);

        console.log("targetWidth:", targetWidth);
        console.log("containerHeight:", containerHeight);
        
        // 设置标记
        // els.canvas._isResetting = true;
        
        // 直接计算中心位置而不是让函数计算
        // const containerWidth = containerRect.width;
        // const targetLeft = Math.round((containerWidth - targetWidth) / 2); 
        // const targetTop = Math.round((containerHeight - containerHeight) / 2);
        
        // 添加回调函数清理标记
        // const cleanupCallback = () => {
        //     els.canvas._isResetting = false;
        //     console.log("重置动画已完成，清理标记");
        // };
        
        const precisionPositionAndSizeResult = positionAndResizeElement(
            els.canvas,           
            "center",                // 改为null，不使用内部定位计算
            dhOffset,            
            dhSize,              
            dhOffsetAndSizeElements, 
            0,                   
            0,                   
            0.1,                
            null,     // 添加回调清理标记
            targetWidth,        
            containerHeight       
        );

        console.log("precisionPositionAndSizeResult:", precisionPositionAndSizeResult);

        return precisionPositionAndSizeResult;
    }

    /**
     * 当窗口大小改变时，确保设置面板保持在视口内
     * @param {HTMLElement} element - 需要调整位置的元素
     * @param {number} margin - 边距，确保元素不会完全贴边
     */
    function windowResizeHandler(element, margin = 20) {
        // 如果模态框未显示，则不处理
        if (element.style.display === 'none') return;
        
        // 获取窗口尺寸
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        
        // 获取元素的当前位置和大小
        const rect = element.getBoundingClientRect();
        const width = rect.width;
        const height = rect.height;
        
        // 计算当前元素的左侧和顶部位置
        // 注意：使用getBoundingClientRect获取的位置，而不是style.left
        let left = rect.left;
        let top = rect.top;
        
        // 保存原始位置，用于判断是否需要调整
        const originalLeft = left;
        const originalTop = top;
        
        // 检查并调整左右位置，确保在可视区域内
        if (left < margin) {
            // 左侧超出视口，调整到左侧margin位置
            left = margin;
        } else if (left + width > windowWidth - margin) {
            // 右侧超出视口，调整到右侧margin位置
            left = windowWidth - width - margin;
        }
        
        // 检查并调整上下位置，确保在可视区域内
        if (top < margin) {
            // 顶部超出视口，调整到顶部margin位置
            top = margin;
        } else if (top + height > windowHeight - margin) {
            // 底部超出视口，调整到底部margin位置
            top = windowHeight - height - margin;
        }
        
        // 如果元素比窗口还大，则优先显示上部和左部
        if (width > windowWidth - margin * 2) {
            left = margin;
        }
        
        if (height > windowHeight - margin * 2) {
            top = margin;
        }
        
        // 只有当位置需要变化时才更新样式
        if (left !== originalLeft || top !== originalTop) {
            console.log(`调整设置面板位置: ${originalLeft},${originalTop} -> ${left},${top}`);
            
            // 更新元素位置（使用绝对定位，确保不受其他因素影响）
            element.style.left = left + 'px';
            element.style.top = top + 'px';
        
        // 清除可能冲突的right/bottom样式
        element.style.right = 'auto';
        element.style.bottom = 'auto';
        }
    }
    /**
     * 初始化Canvas尺寸和位置
     */
    function initializeCanvas(defaultWidth, defaultHeight) {
        setupCanvasSize(defaultWidth, defaultHeight);
        centerCanvasInContainer();
    }

    /**
     * 初始化VAD功能
     * @returns {Promise<boolean>} 初始化是否成功
     */
    function initializeVAD() {
        try {
            // 检查是否已初始化
            if (vadInitialized) {
                return true;
            }
            
            // 初始化VAD
            const result = initVAD({
                ...vadConfig,
                
                // 语音开始回调
                onSpeechStart: handleVADSpeechStart,
                
                // 语音结束回调
                onSpeechEnd: handleVADSpeechEnd,
                
                // VAD误触发回调
                onVADMisfire: () => {
                    console.log("VAD误触发");
                    stopVADRecording();
                },
                
                // 帧处理回调
                onFrameProcessed: handleVADFrameProcessed,
                
                // 错误回调
                onError: (error) => {
                    console.error("VAD错误:", error);
                    showToast(`VAD错误: ${error.message || '未知错误'}`, 'error');
                }
            });
            
            if (result) {
                vadInitialized = true;
                
                // 创建音波可视化组件
                if (els.vadWaveformContainer) {
                    vadWaveform = createAudioWaveform({
                        container: els.vadWaveformContainer,
                        barCount: 5,
                        barWidth: 3,
                        barGap: 3,
                        barHeight: 20,
                        activeColor: '#007AFF',
                        inactiveColor: '#666',
                        minHeight: 8,
                        maxHeight: 20
                    });
                }
                
                // 添加VAD控制按钮事件
                if (els.vadToggleButton) {
                    els.vadToggleButton.addEventListener('click', toggleVAD);
                }
                
                console.log("VAD初始化成功");
                return true;
            } else {
                console.error("VAD初始化失败");
                return false;
            }
        } catch (error) {
            console.error("初始化VAD时出错:", error);
            return false;
        }
    }

    /**
     * 处理VAD检测到语音开始
     */
    function handleVADSpeechStart() {
        console.log("检测到语音开始");
        
        // 如果数字人正在说话，不处理
        if (isDigitalHumanSpeaking) {
            console.log("数字人正在说话，忽略用户语音输入");
            return;
        }
        
        // 更新音波状态
        if (vadWaveform) {
            vadWaveform.updateState(true);
        }
        
        // 开始录音 - 使用预录音缓冲区
        startVADRecording(true);
        
        // 更新UI状态
        updateVADStatusHTML("<span style='color:#2ec4b6'>正在聆听...</span>");
        
        // 如果有VAD状态指示器，更新其状态
        if (els.vadStatusIndicator) {
            els.vadStatusIndicator.classList.add('active');
        }
    }

    /**
     * 处理VAD检测到语音结束
     * @param {Float32Array} audioData 音频数据
     * @param {string} wavBase64 Base64编码的WAV格式音频
     */
    async function handleVADSpeechEnd(audioData, wavBase64) {
        console.log("检测到语音结束，音频长度:", audioData.length);
        
        // 防止重复处理
        if (isProcessingVADResult) {
            return;
        }
        
        isProcessingVADResult = true;
        
        try {
            // 更新音波状态
            if (vadWaveform) {
                vadWaveform.updateState(false);
            }
            
            // 停止录音
            await stopVADRecording();
            
            // 如果音频太短，忽略
            if (audioData.length < 8000) { // 约0.5秒
                console.log("音频太短，忽略");
                updateVADStatusHTML("<span style='color:#ff9f1c'>音频太短，请再说一次</span>");
                isProcessingVADResult = false;
                return;
            }
            
            // 更新UI状态
            updateVADStatusHTML("<span style='color:#4361ee'>正在处理语音...</span>");
            
            // 发送音频数据到语音识别服务
            // 这里使用您项目中已有的ASR功能或调用API
            // 例如：
            // const text = await sendAudioToASR(wavBase64);
            
            // 模拟发送到ASR
            console.log("发送音频数据到ASR服务");
            
            // TODO: 这里应该调用您项目中的ASR功能
            // 示例：如果您有handleASRResult函数处理ASR结果
            // const text = await sendAudioToASR(wavBase64);
            // if (text) {
            //     handleASRResult(text);
            // }
            
            // 更新UI状态
            updateVADStatusHTML("<span style='color:#2ec4b6'>语音已处理</span>");
            
            // 如果有VAD状态指示器，更新其状态
            if (els.vadStatusIndicator) {
                els.vadStatusIndicator.classList.remove('active');
            }
        } catch (error) {
            console.error("处理VAD语音结束事件时出错:", error);
            updateVADStatusHTML(`<span style="color:#f72585">处理语音时出错: ${error.message || '未知错误'}</span>`);
        } finally {
            isProcessingVADResult = false;
        }
    }

    /**
     * 处理VAD帧处理结果
     * @param {Object} probabilities VAD概率数据
     */
    function handleVADFrameProcessed(probabilities) {
        // 更新音波可视化
        if (vadWaveform && vadWaveform.isActive()) {
            vadWaveform.updateState(true, probabilities);
        }
        
        // 检查数字人是否在说话（这里需要根据您的项目实现）
        // 例如，您可以检查音频/视频元素是否在播放
        // checkDigitalHumanSpeaking();
        
    }

    /**
     * 检查数字人是否在说话
     */
    // function checkDigitalHumanSpeaking() {
    //     // 获取相关音频/视频元素
    //     const dhAudioElement = els.audioElement;
    //     const dhVideoElement = els.videoElement;
    //     const mediaElements = document.querySelectorAll('audio, video');
    //     let isSpeaking = false;
        
    //     // 检查主要的数字人音频/视频元素
    //     if (dhSpeakingVolume > 0) {
    //         isSpeaking = true;
    //     } else {
    //         // 检查其他可能的媒体元素（例如动态创建的）
    //         mediaElements.forEach(element => {
    //             // 排除用户的麦克风输入
    //             if (!element.classList.contains('user-mic') && 
    //                 !element.paused && 
    //                 !element.muted && 
    //                 element.volume > 0) {
    //                 isSpeaking = true;
    //             }
    //         });
    //     }
        
    //     // 状态变化处理
    //     if (isSpeaking && !isDigitalHumanSpeaking) {
    //         isDigitalHumanSpeaking = true;
    //         pauseVADIfActive();
    //     } else if (!isSpeaking && isDigitalHumanSpeaking) {
    //         // 添加短暂延迟，避免句子间停顿导致误判
    //         setTimeout(() => {
    //             // 再次检查，确保真的停止了
    //             const stillSpeaking = checkIfStillSpeaking();
    //             if (!stillSpeaking) {
    //                 isDigitalHumanSpeaking = false;
    //                 resumeVADIfPaused();
    //             }
    //         }, 500); // 500ms延迟
    //     }
    // }

    /**
     * 辅助函数：再次检查是否仍在说话
     * @returns {boolean} 是否仍在说话
     */
    function checkIfStillSpeaking() {
        const dhAudioElement = els.dhAudioElement;
        const dhVideoElement = els.dhVideoElement;
        const mediaElements = document.querySelectorAll('audio, video');
        
        if (dhAudioElement && !dhAudioElement.paused && !dhAudioElement.muted) {
            return true;
        } else if (dhVideoElement && !dhVideoElement.paused && !dhVideoElement.muted) {
            return true;
        }
        
        // 检查其他媒体元素
        for (const element of mediaElements) {
            if (!element.classList.contains('user-mic') && 
                !element.paused && 
                !element.muted && 
                element.volume > 0) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 如果VAD活动，暂停它
     */
    function pauseVADIfActive() {
        if (vadActive && !vadPaused) {
            try {
                pauseVAD();
                vadPaused = true;
                console.log("数字人正在说话，VAD已暂停");
                updateVADStatusHTML("<span style='color:#ff9f1c'>数字人正在说话，VAD已暂停</span>");
            } catch (error) {
                console.error("暂停VAD时出错:", error);
            }
        }
    }

    /**
     * 如果VAD被暂停，恢复它
     */
    function resumeVADIfPaused() {
        if (vadActive && vadPaused) {
            try {
                resumeVAD();
                vadPaused = false;
                console.log("数字人停止说话，VAD已恢复");
                updateVADStatusHTML("<span style='color:#2ec4b6'>语音识别已恢复</span>");
            } catch (error) {
                console.error("恢复VAD时出错:", error);
            }
        }
    }

    /**
     * 开始VAD录音
     */
    function startVADRecording(withPreBuffer = false) {
        try {
            if (!isRecording) {
                // startRecording(withPreBuffer);
                start_record();
                console.log("VAD触发录音开始");
            } else if (isRecordingPaused) {
                resumeRecording();
                console.log("VAD触发录音恢复");
            }
        } catch (error) {
            console.error("开始VAD录音时出错:", error);
        }
    }

    /**
     * 停止VAD录音
     */
    async function stopVADRecording() {
        try {
            if (isRecording) {
                // 等语音识别一句结束后再停止录音
                await new Promise(resolve => setTimeout(resolve, 1000));
                await stopRecording();
                console.log("VAD触发录音停止");
            }
        } catch (error) {
            console.error("停止VAD录音时出错:", error);
        }
    }

    /**
     * 切换VAD状态
     */
    function toggleVAD() {
        try {
            if (vadActive) {
                stopVAD();
                vadActive = false;
                vadPaused = false;
                console.log("VAD已停止");
                updateVADStatusHTML("<span style='color:#ff9f1c'>语音监听已关闭</span>");
                
                // 更新按钮状态
                if (els.vadToggleButton) {
                    els.vadToggleButton.textContent = "开启语音监听";
                    els.vadToggleButton.classList.remove('active');
                }
                
                // 更新音波状态
                if (vadWaveform) {
                    vadWaveform.updateState(false);
                }
            } else {
                startVAD();
                vadActive = true;
                console.log("VAD已启动");
                updateVADStatusHTML("<span style='color:#2ec4b6'>语音监听已开启</span>");
                
                // 更新按钮状态
                if (els.vadToggleButton) {
                    els.vadToggleButton.textContent = "关闭语音监听";
                    els.vadToggleButton.classList.add('active');
                }
            }
        } catch (error) {
            console.error("切换VAD状态时出错:", error);
            showToast(`切换语音监听状态失败: ${error.message || '未知错误'}`, 'error');
        }
    }

    /**
     * 更新VAD状态HTML
     * @param {string} html HTML内容
     */
    function updateVADStatusHTML(html) {
        if (els.vadStatus) {
            els.vadStatus.innerHTML = html;
        }
    }

    /**
     * 配置音频元素，强制使用扬声器输出而非听筒
     * @param {HTMLAudioElement} audioElement 要配置的音频元素
     */
    function configureAudioForMobile(audioElement) {
        if (!audioElement) return;
        
        // 设置音频属性，强制使用扬声器
        audioElement.setAttribute('playsinline', '');
        audioElement.setAttribute('webkit-playsinline', '');
        audioElement.setAttribute('x5-playsinline', '');
        audioElement.setAttribute('x-webkit-airplay', 'allow');
        audioElement.setAttribute('x5-video-player-type', 'h5');
        
        // 设置高音量，避免系统自动切换到听筒
        audioElement.volume = 1.0;
        
        // 在iOS上，需要特殊处理
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;
        if (isIOS) {
            // 在iOS上，必须添加这些属性
            audioElement.setAttribute('controls', '');
            audioElement.controls = false; // 隐藏控件但保持属性存在
        }
        
        // 尝试使用setSinkId API（如果浏览器支持）
        if (typeof audioElement.setSinkId === 'function') {
            navigator.mediaDevices.enumerateDevices()
                .then(devices => {
                    // 查找扬声器设备
                    const speakers = devices.filter(device => device.kind === 'audiooutput');
                    if (speakers.length > 0) {
                        // 优先选择非听筒的扬声器
                        const speaker = speakers.find(s => s.label.toLowerCase().includes('speaker')) || speakers[0];
                        return audioElement.setSinkId(speaker.deviceId);
                    }
                })
                .then(() => {
                    console.log('已将音频输出设置为扬声器');
                })
                .catch(err => {
                    console.warn('无法设置音频输出设备:', err);
                });
        }
    }

    //#endregion 方法定义结束

    //#region 音乐板块方法定义
    function addAndPlayBackgroundMusic(data) {
        if (!data || !data.url) {
            console.error('无效的背景音乐消息数据');
            return;
        }
    
        console.log('收到背景音乐消息:', data);
        
        // 从URL中提取文件名作为音乐名称
        const url = data.url || data.message;
        let musicName = url.split('/').pop(); // 获取URL最后部分作为文件名
        
        // 如果文件名中包含查询参数，去除查询参数
        musicName = "生成/检索的音乐："+musicName.split('?')[0];
        
        // 如果传入了显示名称则优先使用
        if (data.name) {
            musicName = data.name;
        }
        
        // 构建音乐项对象
        const musicItem = {
            name: musicName,
            url: url,
            // 如果传入了其他属性，可以在这里添加
            displayTime: data.displayTime || 0
        };
        // 添加到播放列表
        addMusicItem(musicItem);
        
        // 查找选项数量，新添加的项目应该是最后一个
        const index = els.musicPlaylist.options.length - 1;
        
        // 直接点击播放按钮（模拟用户操作）
        try {
            // 选中新添加的音乐
            els.musicPlaylist.selectedIndex = index;
            
            // 查找并点击播放按钮
            console.log('触发播放选中的音乐');
            els.playSelectedBtn.click();

        } catch (e) {
            console.error('自动播放音乐失败:', e);
        }
    }
    
    function addDefaultMusic(path, name = null) {
        // 如果没有提供名称，从路径中提取
        if (!name) {
            const parts = path.split('/');
            name = parts[parts.length - 1];
        }
        
        console.log(`尝试添加预设音乐: ${name} (${path})`);
        
        // 检查播放列表中是否已存在该音乐
        if (addMusicItem) {
            // 创建音频元素加载音频
            const audio = new Audio(path);
            
            // 监听元数据加载完成事件
            audio.addEventListener('loadedmetadata', function() {
                // 创建音乐项
                const musicItem = {
                    name: name,
                    url: path,
                    fileInfo: {
                        name: name,
                        type: 'audio/mp3',
                        lastModified: new Date().getTime(),
                        size: 0
                    }
                };
                
                // 添加到播放列表
                addMusicItem(musicItem);
                console.log(`成功添加预设音乐: ${name}`);
            });
            
            // 处理加载错误
            audio.addEventListener('error', function() {
                console.error(`预设音乐加载失败: ${path}`);
            });
            
            // 加载音频
            audio.load();
        } else {
            console.log('音乐播放器尚未初始化，将在初始化后添加');
            // 添加一个DOMContentLoaded事件监听器
            document.addEventListener('DOMContentLoaded', function checkPlayer() {
                if (addMusicItem) {
                    addDefaultMusic(path, name);
                    document.removeEventListener('DOMContentLoaded', checkPlayer);
                }
            });
        }
                    return true;
    };

    // 暴露全局添加音乐方法，供外部调用
    function addMusicItem(musicItem) {
        // 检查是否已经存在相同名称的音乐
        const exists = playlist.some(item => item.name === musicItem.name);
        if (exists) {
            console.log(`音乐 ${musicItem.name} 已存在于播放列表中`);
            return false;
        }
        
        // 添加到播放列表
        playlist.push(musicItem);
        
        // 更新播放列表显示
        updatePlaylistDisplay();
        
        // 保存播放列表到localStorage
        savePlaylistToStorage();
        
        console.log(`已添加音乐 ${musicItem.name} 到播放列表`);
        return true;
    };

    // 初始化播放器
    function initPlayer() {

        // 设置音量
        els.musicPlayer.volume = els.volumeSlider.value / 100;
        
        addMusicPlayModuleEventListener();
        
        // 不再从localStorage加载播放列表
        // loadPlaylistFromStorage();
        
        // 更新按钮图标
        updateButtonIcons();
        
        // 添加
        addDefaultMusicList();
    }

    // 更新按钮图标
    function updateButtonIcons() {
        // 上一曲按钮图标 - 优化居中
        els.prevTrackBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="19 20 9 12 19 4 19 20"></polygon>
                <line x1="5" y1="19" x2="5" y2="5"></line>
            </svg>
        `;
        
        // 下一曲按钮图标 - 优化居中
        els.nextTrackBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="5 4 15 12 5 20 5 4"></polygon>
                <line x1="19" y1="5" x2="19" y2="19"></line>
            </svg>
        `;
        
        // 初始化播放/暂停按钮状态
        updatePlayPauseButton();
    }

    // 保存播放列表到localStorage（仅保存文件名，不再保存路径以避免安全问题）
    function savePlaylistToStorage() {
        try {
            // 保存名称
            const playlistNames = playlist.map(track => track.name);
            localStorage.setItem('music_playlist_names', JSON.stringify(playlistNames));

            // 不再保存文件URL和详细信息
            // localStorage.setItem('music_playlist_info', JSON.stringify(playlistUrls));
        } catch (e) {
            console.error('保存播放列表失败:', e);
        }
    }

    // 处理文件选择
    function handleMusicFileSelect(event) {
        const files = event.target.files;
        if (files.length === 0) return;
        
        // 不再检查是否有待恢复的播放列表项
        // 不再尝试匹配并恢复播放列表项
        // 直接添加所有文件到播放列表
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (file.type.startsWith('audio/')) {
                playlist.push({
                    name: file.name,
                    file: file,
                    url: URL.createObjectURL(file),
                    fileInfo: {
                        name: file.name,
                        type: file.type,
                        lastModified: file.lastModified,
                        size: file.size
                    }
                });
            }
        }
        
        // 更新播放列表显示
        updatePlaylistDisplay();
        
        // 保存播放列表到localStorage
        savePlaylistToStorage();
        
        // 重置文件输入，以便可以再次选择相同的文件
        els.musicFileInput.value = '';
    }

    // 更新播放列表显示
    function updatePlaylistDisplay() {
        // 清空原始select元素
        els.musicPlaylist.innerHTML = '';
        
        // 清空自定义列表元素
        els.musicPlaylistUl.innerHTML = '';
        
        // 添加新选项
        if (playlist.length === 0) {
            // 如果播放列表为空，显示提示信息
            const emptyItem = document.createElement('li');
            emptyItem.className = 'playlist-empty-message';
            emptyItem.style.cssText = 'padding: 10px; text-align: center; color: var(--text-secondary);';
            emptyItem.textContent = '播放列表为空';
            els.musicPlaylistUl.appendChild(emptyItem);
        } else {
            // 添加新选项到select和自定义列表
            playlist.forEach((track, index) => {
                // 添加到原始select (用于保持兼容性)
                const option = document.createElement('option');
                option.value = index;
                option.textContent = track.name;
                if (index === currentTrackIndex) {
                    option.selected = true;
                }
                // 如果没有文件URL，标记为待恢复
                if (!track.url) {
                    option.disabled = true;
                }
                els.musicPlaylist.appendChild(option);
                
                // 添加到自定义列表
                const li = document.createElement('li');
                li.dataset.index = index;
                li.className = 'playlist-item';
                
                // 应用当前播放和选中状态的类
                if (index === currentTrackIndex) {
                    li.classList.add('playing');
                }
                
                if (index === selectedItemIndex) {
                    li.classList.add('selected');
                }
                
                // 如果没有文件URL，标记为待恢复
                if (!track.url) {
                    li.classList.add('pending-restore');
                }
                
                // 列表项目内容
                const nameSpan = document.createElement('span');
                nameSpan.className = 'track-name';
                nameSpan.textContent = track.name;
                li.appendChild(nameSpan);
                
                // 对于待恢复的文件，添加待恢复标记
                if (!track.url) {
                    const pendingIcon = document.createElement('span');
                    pendingIcon.className = 'pending-icon';
                    pendingIcon.innerHTML = `
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 5px;">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                        </svg>
                    `;
                    pendingIcon.title = "等待恢复文件";
                    li.appendChild(pendingIcon);
                    
                    // 待恢复的项目不能点击
                    li.style.opacity = "0.7";
                    li.style.cursor = "not-allowed";
                } else {
                    // 点击和触摸事件处理
                    function selectItem() {
                        // 更新选中状态
                        const prevSelected = els.musicPlaylistUl.querySelector('.playlist-item.selected');
                        if (prevSelected) {
                            prevSelected.classList.remove('selected');
                        }
                        
                        li.classList.add('selected');
                        selectedItemIndex = index;
                        
                        // 同步更新select的选中项
                        els.musicPlaylist.selectedIndex = index;
                    }
                    
                    // 鼠标点击事件
                    li.addEventListener('click', selectItem);
                    
                    // 移动端触摸事件增强
                    let touchStartTime = 0;
                    let touchTimeout = null;
                    
                    li.addEventListener('touchstart', function(e) {
                        touchStartTime = Date.now();
                        
                        // 300ms后视为长按，选择项目
                        touchTimeout = setTimeout(function() {
                            selectItem();
                            // 添加视觉反馈 - 使用active伪类的效果，无需设置样式
                        }, 300);
                    }, { passive: true });
                    
                    li.addEventListener('touchend', function(e) {
                        const touchDuration = Date.now() - touchStartTime;
                        
                        clearTimeout(touchTimeout);
                        
                        // 短按（小于300ms）且不是滚动操作，视为点击
                        if (touchDuration < 300) {
                            selectItem();
                        }
                        
                        // 双击播放：500ms内两次点击
                        if (li.dataset.lastTouch && Date.now() - li.dataset.lastTouch < 500) {
                            playTrack(index);
                            li.dataset.lastTouch = 0; // 重置，避免连续触发
                        } else {
                            li.dataset.lastTouch = Date.now();
                        }
                    });
                    
                    li.addEventListener('touchmove', function(e) {
                        // 如果是滚动操作，取消长按计时
                        clearTimeout(touchTimeout);
                    }, { passive: true });
                    
                    // 双击播放功能
                    li.addEventListener('dblclick', function() {
                        playTrack(index);
                    });
                }
                
                els.musicPlaylistUl.appendChild(li);
            });
        }
    }

    // 播放指定曲目
    function playTrack(index) {
        if (index < 0 || index >= playlist.length) return;
        
        // 检查是否有URL可播放
        if (!playlist[index].url) {
            els.nowPlayingInfo.textContent = `无法播放:「${playlist[index].name}」- 请先选择音乐文件恢复播放列表`;
            return;
        }
        
        currentTrackIndex = index;
        const track = playlist[index];
        
        // 设置音频源
        els.musicPlayer.src = track.url;
        els.musicPlayer.load();
        
        // 开始播放
        els.musicPlayer.play()
            .then(() => {
                isPlaying = true;
                updatePlayPauseButton();
                els.nowPlayingInfo.textContent = `正在播放: ${track.name}`;
                updatePlaylistDisplay();
                
                // 重置循环计数器
                loopCounter = 0;
            })
            .catch(error => {
                console.error('播放失败:', error);
                els.nowPlayingInfo.textContent = `播放失败: ${error.message}`;
                isPlaying = false;
                updatePlayPauseButton();
            });
    }

    // 播放/暂停切换
    function togglePlayPause() {
        if (playlist.length === 0) return;
        
        if (currentTrackIndex === -1) {
            // 如果没有正在播放的曲目，从列表第一首开始
            playTrack(0);
        } else {
            if (isPlaying) {
                els.musicPlayer.pause();
                isPlaying = false;
                updatePlayPauseButton();
            } else {
                els.musicPlayer.play()
                    .then(() => {
                        isPlaying = true;
                        updatePlayPauseButton();
                    })
                    .catch(error => {
                        console.error('播放失败:', error);
                    });
            }
        }
    }

    // 更新播放/暂停按钮状态
    function updatePlayPauseButton() {
        if (isPlaying) {
            els.playPauseBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="6" y="4" width="4" height="16"></rect>
                    <rect x="14" y="4" width="4" height="16"></rect>
                </svg>
            `;
        } else {
            els.playPauseBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                </svg>
            `;
        }
    }

    // 播放上一曲
    function playPreviousTrack() {
        if (playlist.length === 0) return;
        
        let index = currentTrackIndex - 1;
        if (index < 0) {
            index = playlist.length - 1; // 循环到最后一首
        }
        
        playTrack(index);
    }

    // 播放下一曲
    function playNextTrack() {
        if (playlist.length === 0) return;
        
        let index;
        const loopMode = els.loopModeSelect.value;
        
        if (loopMode === 'random') {
            // 随机播放
            index = Math.floor(Math.random() * playlist.length);
        } else {
            // 顺序播放
            index = currentTrackIndex + 1;
            if (index >= playlist.length) {
                index = 0; // 循环到第一首
            }
        }
        
        playTrack(index);
    }

    // 更新进度条
    function updateProgress(player, percent) {
        // 确保百分比在有效范围内
        percent = Math.max(0, Math.min(1, percent));
        
        // 更新进度条和手柄位置
        const percentStr = `${percent * 100}%`;
        player.progressBar.style.width = percentStr;
        player.progressHandle.style.left = percentStr;
        
        // 更新时间显示
        const time = percent * player.audio.duration;
        const current = formatTime(time);
        const duration = formatTime(player.audio.duration);
        
        // 更新当前时间显示
        player.timeDisplay.children[0].textContent = current;
        // 更新总时间显示
        player.audioInfo.children[1].textContent = `${current} / ${duration}`;
    }

    // 格式化时间（秒 -> MM:SS）
    function formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }

    // 处理曲目播放结束
    function handleTrackEnd() {
        const loopMode = els.loopModeSelect.value;
        const loopCount = parseInt(els.loopCountInput.value);
        const loopInterval = parseInt(els.loopIntervalInput.value);
        
        // 临时设置为非播放状态
        isPlaying = false;
        updatePlayPauseButton();
        
        // 清除可能存在的定时器
        if (loopTimerId) {
            clearTimeout(loopTimerId);
            loopTimerId = null;
        }
        
        if (loopMode === 'single') {
            // 单曲循环
            loopCounter++;
            
            // 检查是否达到循环次数限制
            if (loopCount === 0 || loopCounter < loopCount) {
                // 在间隔后重新播放
                loopTimerId = setTimeout(() => {
                    els.musicPlayer.currentTime = 0;
                    els.musicPlayer.play()
                        .then(() => {
                            isPlaying = true;
                            updatePlayPauseButton();
                        })
                        .catch(error => {
                            console.error('重新播放失败:', error);
                            isPlaying = false;
                            updatePlayPauseButton();
                        });
                }, loopInterval * 1000);
            } else {
                // 循环次数已达上限，播放下一曲
                playNextTrack();
            }
        } else if (loopMode === 'list' || loopMode === 'random') {
            // 列表循环或随机播放
            // 在间隔后播放下一曲
            loopTimerId = setTimeout(() => {
                playNextTrack();
            }, loopInterval * 1000);
        } else if (loopMode === 'no-loop') {
            // 不循环，但如果不是最后一首歌，仍然播放下一首
            if (currentTrackIndex < playlist.length - 1) {
                loopTimerId = setTimeout(() => {
                    playNextTrack();
                }, loopInterval * 1000);
            } else {
                isPlaying = false;
                updatePlayPauseButton();
            }
        }
    }

    function addDefaultMusicList() {
        // 添加默认背景音乐，只需一行代码
        console.log("加载默认背景音乐");
        addDefaultMusic('./static/music/周杰伦 - 断了的弦 (伴奏).ogg');
        addDefaultMusic('./static/music/周杰伦 - 轨迹 (伴奏).ogg');
    
        // 后续如需添加更多预设音乐，只需添加类似下面的代码：
        // addDefaultMusic('./static/music/另一首音乐.mp3');
        // addDefaultMusic('./static/music/custom.mp3', '自定义名称');
    };

    function addMusicPlayModuleEventListener() {
        // 音量控制
        els.volumeSlider.addEventListener('input', function() {
            const volume = this.value;
            els.musicPlayer.volume = volume / 100;
            els.volumeValue.textContent = volume + '%';
        });
        
        // 进度条更新
        els.musicPlayer.addEventListener('timeupdate', function() {
            // 创建一个player对象，包含所需的元素和属性
            const player = {
                audio: els.musicPlayer,
                progressBar: els.progressBar,
                progressHandle: document.createElement('div'), // 创建一个虚拟元素，因为主播放器没有handle元素
                timeDisplay: {
                    children: [els.currentTimeDisplay]
                },
                audioInfo: {
                    children: [document.createElement('span'), els.totalTimeDisplay]
                }
            };
            
            // 计算当前播放进度百分比
            const percent = els.musicPlayer.currentTime / els.musicPlayer.duration || 0;
            
            // 调用updateProgress函数更新进度条
            updateProgress(player, percent);
        });
        
        // 进度条拖动
        els.progressSlider.addEventListener('input', function() {
            const seekTime = (els.musicPlayer.duration * this.value) / 100;
            els.musicPlayer.currentTime = seekTime;
        });
        
        // 播放完成事件
        els.musicPlayer.addEventListener('ended', handleTrackEnd);
        
        // 播放/暂停按钮
        els.playPauseBtn.addEventListener('click', togglePlayPause);
        
        // 上一曲按钮
        els.prevTrackBtn.addEventListener('click', playPreviousTrack);
        
        // 下一曲按钮
        els.nextTrackBtn.addEventListener('click', playNextTrack);
        
        // 文件选择事件
        els.musicFileInput.addEventListener('change', handleMusicFileSelect);
        
        // 播放选中按钮
        els.playSelectedBtn.addEventListener('click', function() {
            // 如果使用新UI中的选中项
            if (selectedItemIndex !== -1) {
                playTrack(selectedItemIndex);
                return;
            }
            
            // 兼容旧逻辑：使用select的选中项
            const selectedIndex = els.musicPlaylist.selectedIndex;
            if (selectedIndex !== -1) {
                playTrack(selectedIndex);
            }
        });
        
        // 移除选中按钮
        els.removeSelectedBtn.addEventListener('click', function() {
            // 如果使用新UI中的选中项
            let selectedIndex = selectedItemIndex;
            
            // 如果没有通过新UI选择，则使用select的选中项
            if (selectedIndex === -1) {
                selectedIndex = els.musicPlaylist.selectedIndex;
            }
            
            if (selectedIndex !== -1) {
                // 如果移除的是当前播放的曲目
                if (selectedIndex === currentTrackIndex) {
                    els.musicPlayer.pause();
                    isPlaying = false;
                    updatePlayPauseButton();
                    currentTrackIndex = -1;
                } else if (selectedIndex < currentTrackIndex) {
                    // 如果移除的曲目在当前播放曲目之前，需要更新索引
                    currentTrackIndex--;
                }
                
                // 从播放列表中移除
                playlist.splice(selectedIndex, 1);
                
                // 重置选中索引
                selectedItemIndex = -1;
                
                // 更新播放列表显示
                updatePlaylistDisplay();
                
                // 保存播放列表到localStorage
                savePlaylistToStorage();
            }
        });
        
        // 清空列表按钮
        els.clearPlaylistBtn.addEventListener('click', function() {
            playlist = [];
            currentTrackIndex = -1;
            selectedItemIndex = -1; // 重置选中索引
            els.musicPlayer.pause();
            isPlaying = false;
            updatePlayPauseButton();
            updatePlaylistDisplay();
            els.nowPlayingInfo.textContent = '未播放任何音乐';
            
            // 保存播放列表到localStorage
            savePlaylistToStorage();
        });
    }
    //#endregion 变量方法结束
    
    
    //#region 数字人基础板块方法调用部分
    initializeDHModules();
    initPlayer();
    
    //#endregion 事件绑定部分结束

    //#region 测试部分待移除代码
    // 在这里定义一个测试消息添加的代码
    
    // start();
    //#endregion

    //#region 测试部分待移除代码
    // 在这里定义一个测试消息添加的代码
    function addExampleMessages() {
        // 清空现有消息
        els.chatContainer.innerHTML = '';
        
        // 添加初始系统消息
        addSystemMessage(`你好！我是智能助手，很高兴为你服务。有什么可以帮助你的吗？`);

        // 添加用户消息
        addUserMessage(`请给我展示一些多媒体内容的例子`);

        // 添加系统消息-多媒体示例1
        addSystemMessage(`
            <div class="rich-text">
                <h3>当然可以！这里是一些多媒体示例：</h3>
                <p><strong>图片示例：</strong></p>
            </div>
            <img src="./static/images/test/wttpssr.png" target="_blank" alt="示例图片">
            <div class="rich-text">
                <p><em>这是一个美丽的风景图片示例</em></p>
            </div>
        `);

        // 添加系统消息-多媒体示例2
        addSystemMessage(`
            <div class="rich-text">
                <p><strong>音频播放器：</strong></p>
            </div>
            <div class="chat-audio-player">
                <audio controls>
                    <source src="data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhC0OT1/LJeywHJHfH8N2QQAkTXr" type="audio/wav">
                    您的浏览器不支持音频播放。
                </audio>
            </div>
            <div class="rich-text">
                <p>这是一个音频文件示例</p>
            </div>
        `);

        // 添加用户消息-带富文本和视频
        addUserMessage(`
            <div class="rich-text">
                <h3>很棒！</h3>
                <p>这些功能看起来都很不错，特别是可以自定义样式的功能。</p>
                <p><strong>谢谢你的演示！</strong></p>
            </div>
            <video controls>
                <source src="https://realszr-res.oss-cn-wuhan-lr.aliyuncs.com/static/videos/dogdog.mp4" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        `);

        // 添加最后的系统消息
        addSystemMessage(`好好好好好`);
    }


    // 调用添加示例消息的方法
    // addExampleMessages();
    
    // start();
    //#endregion


});
